// cc3.js

import { AgentFactory } from './AgentFactory.js';
import { TeamFactory } from './TeamFactory.js';
import { AgencyFactory } from './AgencyFactory.js';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import fs from 'fs/promises';
import { GeminiImageGenerator } from './ig.js';

// Load environment variables
dotenv.config();

async function main() {
  try {
    const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

    if (!GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is not set');
    }

    // Create agent factory with API keys and register the tool
    const agentFactory = new AgentFactory({
      defaultProvider: 'gemini',
      apiKeys: {
        gemini: GEMINI_API_KEY
      }
    });
    
    // Register the image generator tool
    agentFactory.registerTool('imageGenerator', new GeminiImageGenerator());

    // Create team factory
    const teamFactory = new TeamFactory({ agentFactory });

    // Create agency factory
    const agencyFactory = new AgencyFactory({
      teamFactory,
      agentFactory
    });

    // Get the directory name
    const __dirname = path.dirname(fileURLToPath(import.meta.url));

    // Path to the config file
    const configPath = path.join(__dirname, 'cc3.json');

    // Load content creation agency configuration
    const agency = await agencyFactory.loadAgencyFromFile(configPath);

    console.log('Agency loaded successfully:');
    console.log('- name:', agency.name);
    console.log('- team:', Object.keys(agency.team));
    
    const jobName = 'humanContentJob';
    const teamName = 'contentCreationTeam';

    // Create a brief for the job, since the agency requires one.
    agency.createBrief(jobName, {
        title: 'Human-like Content Generation',
        overview: 'A job to generate content that appears more human-like, using a series of specialized agents.'
    });

    // Assign the job to the content creation team
    console.log(`Assigning job "${jobName}" to "${teamName}" (type: team)...`);
    agency.assignJob(jobName, teamName, 'team');

    // Execute the content creation workflow
    console.log('Executing job...');
    
    const results = await agency.execute(jobName);

    console.log('Job execution completed. Results received.');

    console.log('\n=== CONTENT CREATION RESULTS ===\n');

    console.log('Results object contains keys:', Object.keys(results));

    // Display and save results
    let markdownContent = '# CONTENT CREATION RESULTS\n\n';
    markdownContent += `Results object contains keys: [ ${Object.keys(results).join(', ')} ]\n\n`;

    const team = agency.team[teamName];
    for (const job of team.workflow) {
        if (results[job]) {
            const resultText = typeof results[job] === 'object' ? JSON.stringify(results[job], null, 2) : results[job];
            console.log(`${job.replace(/([A-Z])/g, ' $1').trim()} (length: ${resultText.length} characters):`);
            console.log(resultText);
            console.log('\n');
            markdownContent += `## ${job.replace(/([A-Z])/g, ' $1').trim()} (length: ${resultText.length} characters):\n${resultText}\n\n`;
        } else {
            console.log(`WARNING: No ${job} results found!`);
            markdownContent += `## ${job.replace(/([A-Z])/g, ' $1').trim()}:\nWARNING: No results found for this step.\n\n`;
        }
    }

    // Special handling for the image (if it's a path or URL)
    if (results.generateImage && results.generateImage.imagePath) {
        markdownContent += `## Generated Image:\n![Generated Image](${results.generateImage.imagePath})\n\n`;
    } else if (results.generateImage) {
        markdownContent += `## Generated Image (Text Description):\n${JSON.stringify(results.generateImage, null, 2)}\n\n`;
    }

    const resultsFilePath = path.join(__dirname, 'content-results3.md');
    await fs.writeFile(resultsFilePath, markdownContent, 'utf8');
    console.log(`Results saved to ${resultsFilePath}`);

    console.log('Content creation workflow completed successfully!');

  } catch (error) {
    console.error('Error during workflow execution:', error);
    if (error.response && error.response.data) {
      console.error('API Error Details:', error.response.data);
    }
  }
}

main();
