# Agency Event Management Methods

## on(eventN<PERSON>, listener)

**Purpose**: Subscribes to agency events.

**Parameters**:
- `eventName`: String name of the event to subscribe to
- `listener`: Function to call when the event is emitted

**Behavior**: 
1. Registers the listener function to be called when the specified event is emitted
2. Uses the underlying EventEmitter instance to manage subscriptions

**Returns**: Unsubscribe function that can be called to remove the listener.

**Example**:
```javascript
const unsubscribe = agency.on('job:completed', (data) => {
  console.log(`Job ${data.jobId} completed with results:`, data.results);
});

// Later, to unsubscribe
unsubscribe();
```

## broadcastEvent(eventName, data)

**Purpose**: Broadcasts an event to all subscribers.

**Parameters**:
- `eventName`: String name of the event to broadcast
- `data`: Event data to include

**Behavior**: 
1. Emits the event with agency metadata included in the event data:
   - `source`: Set to 'agency'
   - `agency`: The agency name
   - `timestamp`: Current date/time
   - All properties from the provided data object

**Example**:
```javascript
agency.broadcastEvent('system:notification', {
  level: 'info',
  message: 'System maintenance scheduled'
});
```

## subscribeAgent(agentId, eventName, callback)

**Purpose**: Subscribes an agent to an agency event.

**Parameters**:
- `agentId`: String identifier of the agent to subscribe
- `eventName`: String name of the event to subscribe to
- `callback`: Optional function to transform event data before sending to agent (defaults to identity function)

**Behavior**: 
1. Retrieves the agent from the agents collection
2. Validates that the agent exists and supports event subscription
3. Sets up a subscription to the specified event
4. When the event is emitted, transforms the data using the callback function
5. If the transformed data is truthy, forwards the event to the agent

**Returns**: Unsubscribe function that can be called to remove the subscription.

**Example**:
```javascript
// Subscribe the writer agent to job:assigned events, but only for writing jobs
const unsubscribe = agency.subscribeAgent('writer', 'job:assigned', (data) => {
  if (data.jobType === 'writing') {
    return data; // Forward the event
  }
  return null; // Don't forward the event
});
```

## sendMessage(senderId, recipientId, message)

**Purpose**: Sends a message from one agent to another.

**Parameters**:
- `senderId`: String identifier of the sending agent
- `recipientId`: String identifier of the receiving agent
- `message`: Message content to send

**Behavior**: 
1. Validates that both sender and recipient exist in the agents collection
2. Creates a message data object with:
   - `senderId`: ID of the sending agent
   - `recipientId`: ID of the receiving agent
   - `message`: The message content
   - `timestamp`: Current date/time
3. Emits a targeted event for the recipient: `message:{recipientId}`
4. Emits a general event for logging/monitoring: `agent:message`

**Returns**: The message data object.

**Example**:
```javascript
agency.sendMessage('researcher', 'writer', {
  type: 'research_results',
  content: 'Here are the research findings...'
});
```

## onMessage(agentId, listener)

**Purpose**: Subscribes an agent to receive messages.

**Parameters**:
- `agentId`: String identifier of the agent to receive messages
- `listener`: Function to call when a message is received

**Behavior**: 
1. Validates that the agent exists in the agents collection
2. Sets up a subscription to the `message:{agentId}` event
3. When a message is received, calls the listener function with the message data

**Returns**: Unsubscribe function that can be called to remove the subscription.

**Example**:
```javascript
agency.onMessage('writer', (messageData) => {
  console.log(`Writer received message from ${messageData.senderId}:`, messageData.message);
  // Process the message
});
```
