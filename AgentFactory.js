import { Agent } from './Agent.js';
import { <PERSON><PERSON><PERSON><PERSON> } from './GeminiProvider.js';
import { Tool } from './Tool.js';

/**
 * Factory class for creating agents with agent.js
 */
export class AgentFactory {
  /**
   * Create a new AgentFactory
   * @param {Object} config - Configuration object
   * @param {string} config.defaultProvider - Default LLM provider to use
   * @param {Object} config.apiKeys - API keys for different providers
   * @param {Object} [config.tools={}] - Initial tools to register (optional)
   */
  constructor(config) {
    this.defaultProvider = config.defaultProvider || 'gemini';
    this.apiKeys = config.apiKeys || {};
    this.tools = config.tools || {}; // Initialize with any provided tools
  }

  /**
   * Create multiple agents from a configuration object
   * @param {Object} agentsConfig - Object with agent configurations
   * @param {Object} agentsConfig.agentId - Agent configuration for each agent
   * @param {string} agentsConfig.agentId.id - Agent identifier
   * @param {string} agentsConfig.agentId.name - Agent name
   * @param {string} agentsConfig.agentId.description - Agent description
   * @param {string} agentsConfig.agentId.role - Agent role/persona
   * @param {Array<string>} agentsConfig.agentId.goals - Agent goals
   * @param {string} agentsConfig.agentId.provider - LLM provider to use
   * @param {Object} agentsConfig.agentId.llmConfig - LLM configuration
   * @returns {Object} - Object with created agent instances
   * @throws {Error} - If agent creation fails for any agent
   */
  createAgents(agentsConfig) {
    const agents = {};

    for (const [agentId, agentConfig] of Object.entries(agentsConfig)) {
      try {
        agents[agentId] = this.createAgent(agentConfig);
      } catch (error) {
        console.error(`Error creating agent ${agentId}:`, error);
        throw error;
      }
    }

    return agents;
  }

  /**
   * Register a tool with the factory
   */
  registerTool(toolName, toolInstanceOrFunction, config = {}) {
    let tool;
    
    if (toolInstanceOrFunction instanceof Tool) {
      tool = toolInstanceOrFunction;
    } else if (typeof toolInstanceOrFunction === 'function') {
      tool = new Tool({
        name: toolName,
        description: config.description || `Tool: ${toolName}`,
        func: toolInstanceOrFunction,
        ...config
      });
    } else if (typeof toolInstanceOrFunction === 'object' && toolInstanceOrFunction.generateImage) {
      // Handle special case for image generators
      tool = new Tool({
        name: toolName,
        description: config.description || 'Image generation tool',
        func: async (params) => {
          const paramString = typeof params === 'string' ? params : JSON.stringify(params);
          return await toolInstanceOrFunction.generateImage(paramString);
        },
        ...config
      });
    } else {
      throw new Error(`Tool must be a Tool instance, function, or object with callable methods: ${toolName}`);
    }
    
    this.tools[toolName] = tool;
    console.log(`Tool registered: ${toolName}`);
    return tool;
  }

  /**
   * Create tool validator
   */
  createValidator(validatorFn) {
    return validatorFn;
  }

  /**
   * Create tool monitor
   */
  createMonitor(monitorFn) {
    return monitorFn;
  }

  /**
   * Create an agent from a JSON configuration
   * @param {Object} agentConfig - Agent configuration
   * @param {string} agentConfig.id - Agent identifier
   * @param {string} agentConfig.name - Agent name
   * @param {string} agentConfig.description - Agent description
   * @param {string} agentConfig.role - Agent role/persona
   * @param {Array<string>} agentConfig.goals - Agent goals
   * @param {string} agentConfig.provider - LLM provider to use
   * @param {Object} agentConfig.llmConfig - LLM configuration
   * @param {Object} [agentConfig.tools] - Tools available to the agent
   * @returns {Agent} - Created agent instance
   * @throws {Error} - If no API key found for the specified provider
   */
  createAgent(agentConfig) {
    // Ensure the agent has an ID
    if (!agentConfig.id) {
      agentConfig.id = `agent-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    }

    const provider = agentConfig.provider || this.defaultProvider;

    // Try to get API key from the factory config first
    let apiKey = this.apiKeys[provider];

    // If not found, try to get from environment variables
    if (!apiKey) {
      if (provider.toLowerCase() === 'gemini' && process.env.GEMINI_API_KEY) {
        apiKey = process.env.GEMINI_API_KEY;
        console.log('Using GEMINI_API_KEY from environment variables');
      } else if (provider.toLowerCase() === 'openai' && process.env.OPENAI_API_KEY) {
        apiKey = process.env.OPENAI_API_KEY;
        console.log('Using OPENAI_API_KEY from environment variables');
      }
    }

    if (!apiKey) {
      throw new Error(`No API key found for provider: ${provider}. Please set it in the factory config or as an environment variable.`);
    }

    // Create the appropriate LLM provider based on the provider type
    let llmProvider;
    switch (provider.toLowerCase()) {
      case 'gemini':
        const modelName = agentConfig.llmConfig?.model || 'gemini-2.5-flash-lite';
        llmProvider = new GeminiProvider(apiKey, modelName);
        break;
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }

    // Prepare tools for the Agent instance
    const agentTools = {};
    if (agentConfig.tools) {
      for (const [toolNameInAgentConfig, toolRefFromConfig] of Object.entries(agentConfig.tools)) {
        const registeredTool = this.tools[toolRefFromConfig]; // Get the actual instance/function from AgentFactory's registry

        if (registeredTool) {
          // Special handling for the GeminiImageGenerator tool
          if (toolRefFromConfig === 'imageGenerator' && typeof registeredTool.generateImage === 'function') {
            // Create a wrapper function for the agent to call.
            // This wrapper will parse the parameter string (e.g., "{'prompt': '...', 'outputFilename': '...'}")
            // and pass the correct arguments to generateImage.
            agentTools[toolNameInAgentConfig] = async (paramString) => {
              let params = {};
              try {
                // Attempt to parse the paramString as a JSON object (assuming single quotes are used)
                params = JSON.parse(paramString.replace(/'/g, '"'));
              } catch (e) {
                // If it's not a valid JSON string, assume the entire string is the prompt
                console.warn(`Tool parameter for '${toolNameInAgentConfig}' is not valid JSON. Treating as direct prompt string.`, paramString);
                params = { prompt: paramString };
              }

              if (!params.prompt) {
                throw new Error(`Missing 'prompt' parameter for tool '${toolNameInAgentConfig}'.`);
              }

              // Call the actual generateImage method on the registered instance
              // It will use defaults if outputFilename is not provided
              return await registeredTool.generateImage(params.prompt, params.outputFilename, params.outputDir);
            };
          } else if (typeof registeredTool === 'function') {
            // If the registered tool is a direct function, use it as is
            agentTools[toolNameInAgentConfig] = registeredTool;
          } else if (registeredTool && typeof registeredTool.execute === 'function') {
            // If the registered tool is a Tool class instance with execute method
            agentTools[toolNameInAgentConfig] = async (params) => {
              return await registeredTool.execute(params);
            };
          } else {
            console.warn(`Warning: Registered tool '${toolRefFromConfig}' is not a direct function or a recognized class instance with specific methods.`);
          }
        } else {
          console.warn(`Warning: Tool reference '${toolRefFromConfig}' not found in AgentFactory registry.`);
        }
      }
    }

    const agentWithProvider = {
      ...agentConfig,
      llmProvider,
      llmConfig: agentConfig.llmConfig || {},
      tools: agentTools // Pass the prepared/wrapped tools to the Agent instance
    };

    return new Agent(agentWithProvider);
  }
}
