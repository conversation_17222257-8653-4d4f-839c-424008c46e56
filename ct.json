{"agency": {"name": "Content Creation Hub", "description": "An agency focused on generating and refining written content."}, "agents": {"ideaGenerator": {"id": "ideaGenerator", "name": "Idea Generator Bot", "description": "Generates creative and relevant content ideas.", "role": "You are a brainstorming expert. Your role is to come up with a diverse range of compelling blog ideas based on the provided topic and brief. Generate exactly 5 specific content ideas with brief descriptions. Return the ideas as a numbered list.", "goals": ["Generate 5 content ideas.", "Provide brief descriptions for each idea.", "Cover the given topic (AI in content creation: text, images, video)."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 512}}, "contentWriter": {"id": "contentWriter", "name": "Content Writer <PERSON><PERSON>", "description": "Creates well-written and engaging content.", "role": "You are a skilled content writer. Your job is to select exactly one content idea from the provided 'generateIdeas results' and develop ONLY THAT ONE IDEA into a complete article. Read the numbered list of ideas carefully. CHOOSE ONLY ONE NUMBER (e.g., 'idea #1' or 'idea #3') that you find most compelling or relevant. Write a complete 800-word article ONLY about that specific chosen idea. Begin your response by explicitly stating the selected idea, for example: 'I have selected idea #X: [Title of the chosen idea]'. Then, write your complete 800-word article about ONLY that one idea. DO NOT write about multiple ideas, create excerpts for each idea, or ask for additional information. Target a general audience of marketing professionals and tech enthusiasts with an informative and objective tone.", "goals": ["Select one idea from generateIdeas results.", "Write an 800-word article on the chosen idea.", "Target a marketing/tech audience."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.6, "maxOutputTokens": 2048}}, "contentRefiner": {"id": "contentRefiner", "name": "Content Refiner Bot", "description": "Reviews and refines content for clarity, tone, and grammar, and checks word count.", "role": "You are a meticulous content editor. Your task is to refine the provided article for clarity, grammar, spelling, and tone. Ensure it meets the specified target audience (marketing professionals and tech enthusiasts) and maintains an informative and objective tone. Also, verify if the article is approximately 800 words. Provide the polished article.", "goals": ["Refine content for clarity, grammar, and tone.", "Ensure content meets audience and tone requirements.", "Check approximate word count."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.5, "maxOutputTokens": 2048}}, "imagePromptGenerator": {"id": "imagePromptGenerator", "name": "Image Prompt Generator Bo<PERSON>", "description": "Generates detailed prompts for AI image generation based on content.", "role": "You are a creative visual concept artist. Based on the provided article, generate a single, highly detailed and descriptive prompt for an AI image generator (e.g., Midjourney, DALL-E). The prompt should capture the essence of the article's main theme and be suitable for a compelling blog post header image. Focus on visual elements, style, and mood. The prompt should be concise yet rich in detail.", "goals": ["Generate a detailed image prompt.", "Prompt should reflect article's theme.", "Suitable for a blog post header image."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.8, "maxOutputTokens": 256}}, "imageGenerator": {"id": "imageGenerator", "name": "Image Generator Bo<PERSON>", "description": "Generates an image using an AI image generation tool.", "role": "You are an AI image generator. Your task is to take the provided image prompt and use the 'generateImage' tool to create an image. You must call the tool in the format: [TOOL: generateImage({\"query\": \"PROMPT\"})]. Replace PROMPT with the provided image_prompt.", "goals": ["Generate an image from a prompt using the 'generateImage' tool."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 512}, "tools": {"generateImage": "generateImage"}}}, "team": {"contentCreationTeam": {"name": "Content Creation Team", "description": "A team dedicated to generating, writing, and refining content.", "agents": ["ideaGenerator", "contentWriter", "contentRefiner", "imagePromptGenerator", "imageGenerator"], "jobs": {"generateIdeas": {"description": "Generate initial content ideas.", "agent": "ideaGenerator", "inputs": {"topic": "AI in Content Creation", "brief": "Discuss current applications and future potential of AI in content creation across text, images, and video."}}, "writeContent": {"description": "Write the full content based on ideas from generateIdeas.", "agent": "contentWriter", "inputs": {"generateIdeasResults": "{{generateIdeas.output}}"}}, "refineContent": {"description": "Refine the generated article for quality and word count.", "agent": "contentRefiner", "inputs": {"article": "{{writeContent.output}}"}}, "generateImagePrompt": {"description": "Generate an image prompt based on the refined article.", "agent": "imagePromptGenerator", "inputs": {"article": "{{refineContent.output}}"}}, "generateImage": {"description": "Generate an image based on the image prompt.", "agent": "imageGenerator", "inputs": {"image_prompt": "{{generateImagePrompt.output}}"}}}, "workflow": ["generateIdeas", "writeContent", "refineContent", "generateImagePrompt", "generateImage"]}}, "brief": {"blog-post-007": {"title": "The Future of AI in Content Creation", "overview": "Generate a blog post discussing the impact of AI on content creation.", "background": "Audience is marketing professionals and tech enthusiasts.", "objective": "Create an informative and engaging blog post (around 800 words).", "topic": "AI in Content Creation"}}, "assignments": {"blog-post-007": {"assignedTo": "contentCreationTeam", "type": "team"}}}