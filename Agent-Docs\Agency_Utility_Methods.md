# Agency Utility Methods

## formatBriefAsPrompt(brief, inputs)

**Purpose**: Formats a job brief as a prompt for an agent.

**Parameters**:
- `brief`: Brief object to format
- `inputs`: Optional additional inputs to include (defaults to empty object)

**Behavior**: 
1. Creates a formatted prompt string with sections for:
   - Job Brief Title
   - Overview
   - Background
   - Objective
   - Target Audience
   - Preferences (if available in brief or inputs)
   - Dates (if available in brief or inputs)
   - Budget (if available in brief or inputs)
   - Transportation (if available in brief or inputs)
   - Deliverables (if available in brief)
   - Additional Information (if available in brief)
   - Workflow Context (if workflow information is available in inputs)
   - Previous Step Results (if available in inputs)

**Returns**: Formatted prompt string.

**Example**:
```javascript
const brief = {
  title: 'Create Blog Content',
  overview: 'Create engaging blog content for our website',
  background: 'We are a tech company focusing on AI solutions',
  objective: 'Increase website traffic and engagement',
  targetAudience: 'Tech professionals and business leaders',
  deliverables: 'Five 1500-word blog posts on AI topics',
  preferences: 'Conversational tone, include case studies',
  additionalInfo: 'Include links to our product pages where relevant'
};

const inputs = {
  workflowId: 'content-workflow',
  workflowStep: 1,
  previousStepData: {
    topics: ['AI Ethics', 'Machine Learning Basics', 'AI in Healthcare']
  }
};

const prompt = agency.formatBriefAsPrompt(brief, inputs);
console.log(prompt);
```

**Output**:
```
# JOB BRIEF: Create Blog Content

## Overview
Create engaging blog content for our website

## Background
We are a tech company focusing on AI solutions

## Objective
Increase website traffic and engagement

## Target Audience
Tech professionals and business leaders

## Preferences
Conversational tone, include case studies

## Deliverables
Five 1500-word blog posts on AI topics

## Additional Information
Include links to our product pages where relevant

## Workflow Context
This is step 2 of workflow content-workflow.

## Previous Step Results
{
  "topics": [
    "AI Ethics",
    "Machine Learning Basics",
    "AI in Healthcare"
  ]
}
```

## extractInputsFromBrief(brief)

**Purpose**: Extracts inputs from a job brief.

**Parameters**:
- `brief`: Brief object to extract inputs from

**Behavior**: 
1. Creates an empty inputs object
2. Extracts topic from title, overview, or background (specifically looks for 'water bottle' to set topic to 'eco-friendly water bottles')
3. Extracts target audience from targetAudience property
4. Extracts content type from deliverables (specifically looks for 'social media' to set contentType to 'social media posts')
5. Extracts vacation-specific inputs:
   - preferences
   - dates
   - budget
   - transportation

**Returns**: Object containing extracted inputs.

**Example**:
```javascript
const brief = {
  title: 'Water Bottle Marketing Campaign',
  overview: 'Create a marketing campaign for our new eco-friendly water bottles',
  targetAudience: 'Environmentally conscious consumers aged 25-40',
  deliverables: 'Social media posts, blog content, and email newsletter',
  preferences: 'Focus on sustainability features',
  dates: 'Campaign to run from January to March 2023',
  budget: '$5000 for the entire campaign',
  transportation: 'Product samples will be shipped to influencers'
};

const inputs = agency.extractInputsFromBrief(brief);
console.log(inputs);
```

**Output**:
```javascript
{
  topic: 'eco-friendly water bottles',
  targetAudience: 'Environmentally conscious consumers aged 25-40',
  contentType: 'social media posts',
  preferences: 'Focus on sustainability features',
  dates: 'Campaign to run from January to March 2023',
  budget: '$5000 for the entire campaign',
  transportation: 'Product samples will be shipped to influencers'
}
```

## setErrorHandler(jobId, handlerFn)

**Purpose**: Sets an error handler for a job.

**Parameters**:
- `jobId`: String identifier for the job
- `handlerFn`: Error handler function

**Behavior**: 
1. Stores the error handler function in the errorHandlers collection using the job ID as the key
2. The handler function will be called if an error occurs during job execution

**Example**:
```javascript
agency.setErrorHandler('content-creation-123', (error, job) => {
  console.error(`Job ${job.jobId} failed:`, error.message);
  
  // Implement recovery logic
  if (error.message.includes('API rate limit')) {
    // Wait and retry
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(agency.retryJob(job.jobId));
      }, 5000);
    });
  } else {
    // Return partial results or fallback content
    return {
      status: 'partial',
      message: 'Completed with fallback content due to error',
      content: 'Default content as fallback'
    };
  }
});
```

## defineJobSchema(jobId, inputSchema, outputSchema)

**Purpose**: Defines a schema for job inputs and outputs.

**Parameters**:
- `jobId`: String identifier for the job
- `inputSchema`: Schema for job inputs
- `outputSchema`: Schema for job outputs

**Behavior**: 
1. Stores the input and output schemas in the jobSchemas collection using the job ID as the key
2. These schemas will be used to validate inputs and outputs during job execution

**Example**:
```javascript
agency.defineJobSchema(
  'content-creation-123',
  {
    // Input schema
    topic: { type: 'string', required: true },
    wordCount: { type: 'number', required: true },
    tone: { type: 'string', enum: ['formal', 'casual', 'technical'] }
  },
  {
    // Output schema
    content: { type: 'string', required: true },
    metadata: { type: 'object' },
    wordCount: { type: 'number', required: true }
  }
);
```

## _validateAgainstSchema(data, schema)

**Purpose**: Validates data against a schema.

**Parameters**:
- `data`: Data to validate
- `schema`: Schema to validate against

**Behavior**: 
1. If no schema is provided, returns { isValid: true, errors: [] }
2. For each field in the schema:
   - Checks if required fields are present
   - Checks if field types match the schema
   - Checks if field values are in the allowed enum values
3. Collects all validation errors

**Returns**: Validation result object with:
- `isValid`: Boolean indicating if the data is valid
- `errors`: Array of error messages

**Example** (internal usage):
```javascript
// This is a private method used internally by the execute method
const validation = this._validateAgainstSchema(
  { topic: 'AI', wordCount: '1500' }, // Data to validate
  { topic: { type: 'string', required: true }, wordCount: { type: 'number', required: true } } // Schema
);

if (!validation.isValid) {
  console.error('Validation errors:', validation.errors);
  // Would output: ["Field 'wordCount' should be of type 'number', but got 'string'"]
}
```
