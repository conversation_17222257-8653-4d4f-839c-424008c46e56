# Getting Started with Multi-Agent Framework

Welcome to the Multi-Agent Framework! This guide will help you set up and run your first multi-agent workflow in minutes.

## Table of Contents
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [API Key Setup](#api-key-setup)
- [Basic Concepts](#basic-concepts)
- [Your First Workflow](#your-first-workflow)
- [Understanding the Example](#understanding-the-example)
- [Next Steps](#next-steps)

## Prerequisites

Before you begin, make sure you have:

- **Node.js** (version 16 or higher)
- **npm** (comes with Node.js)
- **Google Gemini API Key** (free tier available)

## Installation

1. **Clone or download the framework** to your local machine

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Verify installation**:
   ```bash
   node --version
   npm --version
   ```

## API Key Setup

The framework uses Google's Gemini API for AI capabilities. Here's how to get your API key:

1. **Get a Gemini API Key**:
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Sign in with your Google account
   - Click "Create API Key"
   - Copy the generated key

2. **Set up environment variables**:
   Create a `.env` file in your project root:
   ```bash
   # .env
   GEMINI_API_KEY=your_actual_api_key_here
   ```

   ⚠️ **Important**: Never commit your `.env` file to version control!

## Basic Concepts

The framework is built around these core concepts:

### 🤖 **Agents**
Individual AI entities with specific roles and capabilities:
- **Role**: What the agent does (e.g., "content writer", "idea generator")
- **Goals**: What the agent aims to achieve
- **LLM Provider**: The AI model it uses (currently Gemini)
- **Tools**: External capabilities it can access

### 👥 **Teams**
Groups of agents working together:
- **Workflow**: Sequence of jobs to execute
- **Collaboration**: Agents can share information and results
- **Specialization**: Each agent handles specific tasks

### 🏢 **Agency**
The orchestrator that manages everything:
- **Memory Management**: Stores conversation history and data
- **Job Assignment**: Routes work to appropriate agents/teams
- **Error Handling**: Manages failures and retries
- **Event System**: Coordinates communication between components

### 📋 **Jobs**
Individual tasks within a workflow:
- **Input**: Data the job needs to process
- **Assignee**: Which agent or team handles it
- **Output**: Results passed to the next job

### 🔄 **Workflows**
Sequences of jobs that achieve a goal:
- **Linear**: Jobs run one after another
- **Dependencies**: Later jobs use outputs from earlier ones
- **Error Recovery**: Failed jobs can be retried or replanned

## Your First Workflow

The framework comes with a complete example that creates a blog post with an image. Let's run it:

### 1. **Run the Example**
```bash
node cc.js
```

### 2. **What Happens**
The example executes a content creation workflow:

1. **Idea Generator** → Creates 5 blog post ideas
2. **Content Writer** → Selects one idea and writes an 800-word article
3. **Content Refiner** → Edits and polishes the article
4. **Image Prompt Generator** → Creates a description for an image
5. **Image Generator** → Generates an actual image

### 3. **View Results**
- **Console Output**: See real-time progress and results
- **Generated Files**: 
  - `content-results.md` - Complete workflow results
  - `generated_images/` - Generated images

## Understanding the Example

Let's break down the key components:

### Configuration (`cc.json`)
The workflow is defined in a JSON configuration file:

```json
{
  "agency": {
    "name": "Content Creation Hub",
    "description": "An agency focused on generating content"
  },
  "agents": {
    "ideaGenerator": {
      "id": "ideaGenerator",
      "name": "Idea Generator Bot",
      "role": "You are a brainstorming expert...",
      "goals": ["Generate 5 content ideas"],
      "provider": "gemini"
    }
    // ... more agents
  },
  "team": {
    "contentCreationTeam": {
      "name": "Content Creation Team",
      "agents": ["ideaGenerator", "contentWriter", "contentRefiner"],
      "workflow": ["generateIdeas", "writeContent", "refineContent"]
    }
  }
}
```

### Main Script (`cc.js`)
The script that orchestrates everything:

```javascript
// 1. Set up factories
const agentFactory = new AgentFactory({ apiKeys: { gemini: GEMINI_API_KEY } });
const teamFactory = new TeamFactory({ agentFactory });
const agencyFactory = new AgencyFactory({ teamFactory, agentFactory });

// 2. Load configuration
const agency = await agencyFactory.loadAgencyFromFile('cc.json');

// 3. Assign and execute job
agency.assignJob('blog-post-007', 'contentCreationTeam', 'team');
const results = await agency.execute('blog-post-007', { topic: 'AI in Content Creation' });
```

### Key Features Demonstrated

- **Multi-Agent Collaboration**: 5 agents working together
- **Sequential Workflow**: Each step builds on the previous
- **Tool Integration**: Image generation using external API
- **Memory Management**: Information flows between jobs
- **Error Handling**: Robust execution with fallbacks
- **Output Generation**: Structured results in multiple formats

## Next Steps

Now that you've run your first workflow, here are some ways to explore further:

### 🎯 **Modify the Example**
- Change the blog topic in `cc.json`
- Adjust agent roles and goals
- Add new agents to the team
- Modify the workflow sequence

### 🔧 **Create Your Own Workflow**
1. **Define your agents** with specific roles
2. **Create a team** with a workflow
3. **Write a configuration** file
4. **Build a script** to run it

### 📚 **Explore the Framework**
- **`Agency.js`** - Core orchestration logic
- **`Agent.js`** - Individual agent implementation
- **`Team.js`** - Team management
- **`GeminiProvider.js`** - AI model integration

### 🛠 **Advanced Features**
- **Custom Tools**: Add your own external integrations
- **Memory Scopes**: Manage data across workflows
- **Error Handlers**: Custom failure recovery
- **Event System**: Monitor and debug workflows

## Troubleshooting

### Common Issues

**"GEMINI_API_KEY environment variable is not set"**
- Make sure your `.env` file exists and contains the API key
- Verify the key is valid and has sufficient quota

**"API call failed"**
- Check your internet connection
- Verify your API key has remaining quota
- Try again (the framework includes retry logic)

**"No results found for this step"**
- Check that all agents are properly configured
- Verify the workflow sequence in your config
- Look for error messages in the console output

### Getting Help

- Check the console output for detailed error messages
- Verify your configuration file syntax
- Ensure all required dependencies are installed
- Review the API documentation for advanced features

---

🎉 **Congratulations!** You've successfully set up and run your first multi-agent workflow. The framework is now ready for your creative projects! 