// calculator.js

import * as math from 'mathjs';

/**
 * Calculator tool that evaluates mathematical expressions using mathjs
 * @param {string} expression - The mathematical expression to evaluate
 * @returns {string} The result of the calculation
 */
export function calculatorTool(expression) {
  try {
    // Clean the expression
    const cleanExpression = expression.trim();

    // Handle special cases
    let processedExpression = cleanExpression;

    // Replace "sine of X degrees" with sin(X deg)
    if (processedExpression.match(/sine of (\d+) degrees/i)) {
      processedExpression = processedExpression.replace(
        /sine of (\d+) degrees/i,
        'sin($1 deg)'
      );
    }

    // Handle natural language expressions
    if (processedExpression.match(/^(calculate|compute|evaluate|what is|find|solve)\s+/i)) {
      processedExpression = processedExpression.replace(
        /^(calculate|compute|evaluate|what is|find|solve)\s+/i,
        ''
      );
    }

    // Evaluate the expression using mathjs
    const result = math.evaluate(processedExpression);

    // Handle different result types
    if (math.typeOf(result) === 'Complex') {
      return `${result.toString()}`;
    } else if (math.typeOf(result) === 'BigNumber') {
      return result.toString();
    } else if (Array.isArray(result)) {
      return JSON.stringify(result);
    } else if (typeof result === 'object') {
      return JSON.stringify(result);
    }

    // Handle floating-point precision issues for trigonometric functions
    if (processedExpression.includes('sin') ||
        processedExpression.includes('cos') ||
        processedExpression.includes('tan') ||
        cleanExpression.includes('sine of')) {
      // Round to 10 decimal places to avoid floating-point precision issues
      const roundedResult = Math.round(result * 1e10) / 1e10;
      // Special case for common values
      if (Math.abs(roundedResult - 0.5) < 1e-10) return '0.5';
      if (Math.abs(roundedResult - 1) < 1e-10) return '1';
      if (Math.abs(roundedResult - 0) < 1e-10) return '0';
      return roundedResult.toString();
    }

    return result.toString();
  } catch (error) {
    throw new Error(`Calculator error: ${error.message}`);
  }
}

