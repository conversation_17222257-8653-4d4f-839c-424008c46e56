// cc.js

import { AgentFactory } from './AgentFactory.js';
import { TeamFactory } from './TeamFactory.js';
import { AgencyFactory } from './AgencyFactory.js';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs/promises';
import { GeminiImageGenerator } from './ig.js'; // Assuming ig.js exports this class

async function main() {
  try {
    const GEMINI_API_KEY = process.env.GEMINI_API_KEY || 'AIzaSyD8X1n5mQT-7kNWn0n5ym4to3P7I5k3k0g';

    if (!GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is not set');
    }

    // Create agent factory with API keys and register the tool
    const agentFactory = new AgentFactory({
      defaultProvider: 'gemini',
      apiKeys: {
        gemini: GEMINI_API_KEY
      }
    });

    // --- MODIFICATION START ---

    // Create a single instance of the GeminiImageGenerator
    const geminiImageGeneratorInstance = new GeminiImageGenerator();

    // Register the image generator tool with a function that calls the instance's method
    const imageGeneratorTool = agentFactory.registerTool('generateImage', async (params) => {
        // Handle different parameter formats
        let prompt;
        if (typeof params === 'string') {
            prompt = params;
        } else if (params && typeof params.prompt === 'string') {
            prompt = params.prompt;
        } else if (params && typeof params === 'object') {
            // Try to find the prompt in the first property
            const values = Object.values(params);
            prompt = values.find(v => typeof v === 'string' && v.length > 10);
        }

        if (!prompt || typeof prompt !== 'string') {
            throw new Error('Invalid or missing "prompt" parameter for generateImage tool.');
        }

        return await geminiImageGeneratorInstance.generateImage(prompt, params.outputFilename, params.outputDir);
    }, {
      description: 'Generates an image based on a detailed text prompt. Returns a path or URL to the generated image.',
      isAsync: true, // Image generation is typically an async operation
      timeout: 30000 // Increased timeout for image generation
    });

    // Add validator for the imageGenerator tool
    imageGeneratorTool.addValidator((params) => {
      let prompt;
      if (typeof params === 'string') {
        prompt = params;
      } else if (params && typeof params.prompt === 'string') {
        prompt = params.prompt;
      } else if (params && typeof params === 'object') {
        const values = Object.values(params);
        prompt = values.find(v => typeof v === 'string' && v.length > 10);
      }

      if (!prompt || typeof prompt !== 'string' || prompt.trim().length < 10) {
        throw new Error('Image prompt must be a non-empty string and at least 10 characters long.');
      }
    });

    // Add monitor for the imageGenerator tool
    imageGeneratorTool.addMonitor((toolName, params, duration, success, result, error) => {
      console.log(`Tool ${toolName} executed in ${duration}ms, success: ${success}`);
      if (success && result && result.imagePath) {
        console.log(`Generated image available at: ${result.imagePath}`);
      }
      if (error) {
        console.log(`Error during image generation: ${error.message}`);
      }
    });

    // --- MODIFICATION END ---

    // Create team factory
    const teamFactory = new TeamFactory({ agentFactory });

    // Create agency factory
    const agencyFactory = new AgencyFactory({
      teamFactory,
      agentFactory
    });

    // Get the directory name
    const __dirname = path.dirname(fileURLToPath(import.meta.url));

    // Path to the config file
    const configPath = path.join(__dirname, 'ct.json');

    // Load content creation agency configuration
    const agency = await agencyFactory.loadAgencyFromFile(configPath);

    console.log('Agency loaded successfully:');
    console.log('- name:', agency.name);
    console.log('- team:', Object.keys(agency.team));
    console.log('- brief:', Object.keys(agency.brief));

    console.log('Executing brief: blog-post-007');

    // Extract inputs from the brief for the blog post
    const brief = agency.brief['blog-post-007'];

    // Assign the job to the content creation team
    console.log('Assigning job "blog-post-007" to "contentCreationTeam" (type: team)...');
    agency.assignJob('blog-post-007', 'contentCreationTeam', 'team');

    // Execute the content creation workflow with the initial brief inputs
    console.log('Executing job...');
    console.log('Using initial inputs for the workflow:');
    console.log('- topic:', brief.topic);
    console.log('- brief:', brief.overview);

    const results = await agency.execute('blog-post-007', {
      topic: brief.topic,
      brief: brief.overview
    });

    console.log('Job execution completed. Results received.');

    console.log('\n=== CONTENT CREATION RESULTS ===\n');

    console.log('Results object contains keys:', Object.keys(results));

    // Display and save results
    let markdownContent = '# CONTENT CREATION RESULTS\n\n';
    markdownContent += `Results object contains keys: [ ${Object.keys(results).join(', ')} ]\n\n`;

    const team = agency.team['contentCreationTeam'];
    for (const jobName of team.workflow) {
        if (results[jobName]) {
            const resultText = typeof results[jobName] === 'object' ? JSON.stringify(results[jobName], null, 2) : results[jobName];
            console.log(`${jobName.replace(/([A-Z])/g, ' $1').trim()} (length: ${resultText.length} characters):`);
            console.log(resultText);
            console.log('\n');
            markdownContent += `## ${jobName.replace(/([A-Z])/g, ' $1').trim()} (length: ${resultText.length} characters):\\n${resultText}\\n\\n`;
        } else {
            console.log(`WARNING: No ${jobName} results found!`);
            markdownContent += `## ${jobName.replace(/([A-Z])/g, ' $1').trim()}:\\nWARNING: No results found for this step.\\n\\n`;
        }
    }

    // Special handling for the image (if it's a path or URL)
    if (results.generateImage && results.generateImage.imagePath) {
        markdownContent += `## Generated Image:\\n![Generated Image](${results.generateImage.imagePath})\\n\\n`;
    } else if (results.generateImage) {
        markdownContent += `## Generated Image (Text Description):\\n${JSON.stringify(results.generateImage, null, 2)}\\n\\n`;
    }

    const resultsFilePath = path.join(__dirname, 'content-results.md');
    await fs.writeFile(resultsFilePath, markdownContent, 'utf8');
    console.log(`Results saved to ${resultsFilePath}`);

    console.log('Content creation workflow completed successfully!');

  } catch (error) {
    console.error('Error during workflow execution:', error);
    if (error.response && error.response.data) {
      console.error('API Error Details:', error.response.data);
    }
  }
}

main();