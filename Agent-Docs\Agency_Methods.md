# Agency.js Methods Documentation

## Core Methods

### constructor(config)
Creates a new Agency instance with the specified configuration. Initializes core components like agents, team, brief, and active jobs, as well as enhanced components like event emitter, memory management, job contexts, workflows, and error handlers.

### addAgent(id, agent)
Adds an agent to the agency with the specified ID. Sets up event subscriptions to relay agent events through the agency's event system. Returns the agency instance for method chaining.

### addTeam(id, team)
Adds a team to the agency with the specified ID. Returns the agency instance for method chaining.

## Event Management

### on(eventName, listener)
Subscribes to agency events. Returns an unsubscribe function that can be called to remove the listener.

### broadcastEvent(eventName, data)
Broadcasts an event to all subscribers with agency metadata included in the event data.

### subscribeAgent(agentId, eventName, callback)
Subscribes an agent to an agency event. Optionally transforms event data before sending it to the agent. Returns an unsubscribe function.

### sendMessage(senderId, recipientId, message)
Sends a message from one agent to another. Validates that both sender and recipient exist. Emits events for the message.

### onMessage(agentId, listener)
Subscribes an agent to receive messages. Returns an unsubscribe function.

## Memory Management

### createMemoryScope(scopeId, config)
Creates a new memory scope with the specified ID and configuration. Returns the created memory scope.

### getMemoryScope(scopeId)
Retrieves a memory scope by ID. Throws an error if the scope doesn't exist.

### shareMemoryBetween(sourceId, targetId, keys, accessMode)
Shares memory between scopes with access control. Can share specific keys or all keys, with read-only or read-write access.

### getMemoryAccessInfo(scopeId, key)
Gets memory access control information for a specific scope and key.

## Job Management

### createBrief(jobId, briefData)
Creates a brief for a job with the specified ID and data. Returns the created brief.

### createJobContext(jobId, initialContext)
Creates a job context for sharing data between agents working on the same job. Returns the created context.

### updateJobContext(jobId, updates)
Updates a job context with the specified updates. Returns the updated context.

### getJobContext(jobId)
Gets a job context by ID. Throws an error if the context doesn't exist.

### assignJob(jobId, assigneeId, assigneeType)
Assigns a job to an agent or team. Creates a job context if it doesn't exist. Returns job information.

### setErrorHandler(jobId, handlerFn)
Sets an error handler function for a specific job.

### setWorkflowErrorHandler(workflowId, handlerFn)
Sets an error handler function for a specific workflow.

### defineJobSchema(jobId, inputSchema, outputSchema)
Defines a schema for job inputs and outputs for validation purposes.

### _validateAgainstSchema(data, schema)
Private method that validates data against a schema. Returns validation result with isValid flag and errors.

### execute(jobId, additionalInputs)
Executes a job with the specified ID and additional inputs. Validates inputs and outputs against schemas if defined. Returns job results.

### retryJob(jobId, maxRetries, additionalInputs)
Retries a failed job up to the specified maximum number of retries. Returns job results.

### getJobsByStatus(status)
Returns all jobs with the specified status.

### getJob(jobId)
Gets a job by ID.

### cleanupJob(jobId, keepResults)
Cleans up resources for a completed job. Optionally keeps job results in global memory.

## Workflow Management

### executeWorkflow(workflowDefinition, workflowId, initialData)
Executes a workflow of jobs (sequential, parallel, or conditional). Creates a memory scope for the workflow. Returns workflow results.

### cleanupWorkflow(workflowId, keepResults)
Cleans up resources for a completed workflow. Optionally keeps workflow results in global memory.

### planAndExecuteWorkflow(agentId, goal, options)
Plans and optionally executes a workflow from a high-level goal. Returns planned jobs and workflow ID if executed.

### planJobsFromGoal(agentId, goal)
Plans jobs from a high-level goal using an agent. Returns an array of planned job IDs.

## Utility Methods

### formatBriefAsPrompt(brief, inputs)
Formats a job brief as a prompt for an agent, including various sections like overview, background, objective, etc.

### extractInputsFromBrief(brief)
Extracts inputs from a job brief. Returns an object containing extracted inputs.


