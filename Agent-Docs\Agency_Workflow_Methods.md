# Agency Workflow Management Methods

## executeWorkflow(workflowDefinition, workflowId, initialData)

**Purpose**: Executes a workflow of jobs (sequential, parallel, or conditional).

**Parameters**:
- `workflowDefinition`: Array of workflow step definitions
- `workflowId`: String identifier for the workflow
- `initialData`: Optional initial data for the workflow (defaults to empty object)

**Behavior**: 
1. Creates a workflow record with:
   - `id`: The provided workflow ID
   - `definition`: The provided workflow definition
   - `status`: 'in_progress'
   - `startedAt`: Current date/time
   - `currentStep`: 0
   - `results`: Empty object
   - `error`: null
2. Emits a 'workflow:started' event with the workflow ID and number of steps
3. Creates a memory scope for the workflow
4. Stores initial data in the workflow memory
5. Executes each step in the workflow definition:
   - **Sequential steps**: Default execution mode where steps run one after another
   - **Parallel steps**: Multiple jobs executed simultaneously
   - **Conditional steps**: Jobs executed only if a condition function returns true
6. For each step:
   - Updates the workflow status with the current step
   - Emits a 'workflow:step' event with step details
   - Creates a brief if it doesn't exist
   - Assigns the job to the specified assignee
   - Executes the job with appropriate inputs
   - Stores results in workflow memory and the workflow results object
   - Checks if the job failed and throws an error if it did
7. After all steps complete:
   - Updates the workflow status to 'completed'
   - Sets the completion time
   - Emits a 'workflow:completed' event with the workflow ID and results
8. If an error occurs:
   - Updates the workflow status to 'failed'
   - Sets the error message
   - Emits a 'workflow:failed' event with the workflow ID, error message, and step
   - Calls the workflow error handler if defined

**Returns**: Promise resolving to an object with:
- `status`: 'completed' or 'failed'
- `workflowId`: The workflow ID
- `results`: The workflow results (for completed workflows)
- `error`: The error message (for failed workflows)
- `step`: The step where the error occurred (for failed workflows)

**Example**:
```javascript
const workflowDefinition = [
  {
    // Step 1: Research
    jobId: 'research-job',
    assigneeId: 'researcher',
    assigneeType: 'agent',
    brief: {
      title: 'Research AI Topics',
      overview: 'Research current AI trends',
      objective: 'Identify top 5 AI trends'
    }
  },
  {
    // Step 2: Content Creation (runs only if research was successful)
    jobId: 'content-job',
    assigneeId: 'writer',
    assigneeType: 'agent',
    type: 'conditional',
    condition: (data) => data.researchComplete === true,
    brief: {
      title: 'Create Content',
      overview: 'Create blog posts based on research',
      objective: 'Write 5 blog posts'
    }
  },
  {
    // Step 3: Parallel Review and SEO Optimization
    type: 'parallel',
    jobId: ['review-job', 'seo-job'],
    assigneeId: ['editor', 'seo-specialist'],
    assigneeType: ['agent', 'agent'],
    brief: [
      {
        title: 'Review Content',
        overview: 'Review and edit blog posts',
        objective: 'Ensure quality and accuracy'
      },
      {
        title: 'SEO Optimization',
        overview: 'Optimize content for search engines',
        objective: 'Improve search rankings'
      }
    ]
  }
];

agency.executeWorkflow(workflowDefinition, 'content-workflow', {
  topic: 'Artificial Intelligence',
  targetWordCount: 1500
})
.then(result => {
  console.log('Workflow completed:', result);
})
.catch(error => {
  console.error('Workflow execution failed:', error);
});
```

## setWorkflowErrorHandler(workflowId, handlerFn)

**Purpose**: Sets an error handler for a workflow.

**Parameters**:
- `workflowId`: String identifier for the workflow
- `handlerFn`: Error handler function

**Behavior**: 
1. Stores the error handler function in the workflowErrorHandlers collection using the workflow ID as the key
2. The handler function will be called if an error occurs during workflow execution

**Example**:
```javascript
agency.setWorkflowErrorHandler('content-workflow', (error, workflow) => {
  console.error(`Workflow ${workflow.id} failed at step ${workflow.currentStep}:`, error.message);
  
  // Implement recovery logic
  if (workflow.currentStep < 2) {
    // Try an alternative approach for early steps
    return agency.executeWorkflow(alternativeWorkflow, `${workflow.id}-recovery`);
  } else {
    // For later steps, return partial results
    return {
      status: 'partial',
      workflowId: workflow.id,
      completedSteps: workflow.currentStep,
      partialResults: workflow.results
    };
  }
});
```

## cleanupWorkflow(workflowId, keepResults)

**Purpose**: Cleans up resources for a completed workflow.

**Parameters**:
- `workflowId`: String identifier for the workflow
- `keepResults`: Whether to keep workflow results in global memory (defaults to true)

**Behavior**: 
1. Retrieves the workflow with the specified ID from the workflows collection
2. If the workflow doesn't exist, returns false
3. If the workflow status is not 'completed' or 'failed', returns false
4. Stores the results if keepResults is true
5. Removes the workflow memory scope from the memoryScopes collection
6. Removes the workflow error handler from the workflowErrorHandlers collection
7. Removes the workflow from the workflows collection
8. Emits a 'workflow:cleaned' event with the workflow ID and keepResults flag
9. If keeping results, stores them in global memory

**Returns**: Boolean indicating success.

**Example**:
```javascript
const success = agency.cleanupWorkflow('content-workflow', true);
if (success) {
  console.log('Workflow resources cleaned up successfully');
}
```
