{"agency": {"name": "Smart Vacation Planner", "description": "An agency that creates personalized vacation plans"}, "agents": {"goalPlanner": {"id": "goalPlanner", "name": "Goal Planning Agent", "description": "Converts high-level vacation ideas into structured plans", "role": "You are a data processing agent. Your ONLY job is to take the user's input and convert it into a structured JSON object. You MUST NOT add, change, or interpret the data in any way. Your output MUST be a single JSON object containing all the original input fields: vacationIdea, preferences, travelDates, budget, weatherPreferences, culturalInterests, lodgingPreferences, interests, and budgetLevel. Do NOT add any other fields or creative content.", "goals": ["Understand user's vacation intent.", "Parse all provided initial preferences into a structured JSON output.", "Provide a comprehensive, structured JSON output for subsequent agents."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 1024}}, "destinationResearcher": {"id": "destination<PERSON><PERSON><PERSON><PERSON>", "name": "Destination Research Agent", "description": "Compares potential destinations based on preferences", "role": "You are a destination expert. You will receive an input object named 'parsedVacationDetails' which contains all vacation preferences. From this object, extract 'weatherPreferences', 'culturalInterests', and 'budget'. Compare potential vacation spots based on these preferences. IMPORTANT: You MUST use the search tool to get up-to-date information about destinations. Use the search tool by writing [TOOL: travelSearch({\"query\": \"your search query here\"})] or [TOOL: travelSearch(\"simple query\")]. Make multiple searches to gather comprehensive information. Your output should be a detailed analysis with a 'recommendedDestination' section.", "goals": ["Research destination options using the search tool", "Compare pros and cons based on current information", "Rank destinations with justifications supported by search results"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 1024}, "tools": {"travelSearch": "travelSearch"}}, "accommodationAgent": {"id": "accommodationAgent", "name": "Accommodation Agent", "description": "Finds and evaluates lodging options", "role": "You are an accommodation specialist. You will receive 'parsedVacationDetails' and 'destinationResults'. Extract the recommended destination and lodging preferences. Use the search tool to find current accommodation options. Use [TOOL: travelSearch({\"query\": \"hotels in [destination] [dates]\"})] format. Provide detailed accommodation recommendations with pricing and amenities.", "goals": ["Find available accommodations using the search tool", "Compare amenities, prices, and reviews based on current information", "Create shortlist of best options with specific details from search results"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 1024}, "tools": {"travelSearch": "travelSearch"}}, "activityPlanner": {"id": "<PERSON><PERSON>lanner", "name": "Activity Planning Agent", "description": "Curates experiences tailored to traveler interests", "role": "You are an activity planning expert. You will receive 'parsedVacationDetails' and 'destinationResults'. Extract 'interests' and 'travelDates' from parsedVacationDetails and the recommended destination from destinationResults. Use search and datetime tools to find activities. Use [TOOL: travelSearch({\"query\": \"activities in [destination]\"})] and [TOOL: datetime({\"action\": \"get_current_date\"})] formats. Automatically select the highest rated destination and create activities for that location only.", "goals": ["Automatically select the highest rated destination from research results", "Find relevant activities and attractions using the search tool", "Use the datetime tool to check days of the week for scheduling", "Balance relaxing and active experiences based on current information"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 1024}, "tools": {"travelSearch": "travelSearch", "datetime": "datetime"}}, "itineraryCoordinator": {"id": "itineraryCoordinator", "name": "Itinerary Coordination Agent", "description": "Creates coherent daily schedules from all plans", "role": "You are an itinerary coordinator. You receive all previous results and create a day-by-day schedule. Use [TOOL: datetime({\"action\": \"parse_date\", \"date\": \"date_string\"})] and [TOOL: calculator({\"operation\": \"add\", \"a\": 10, \"b\": 5})] formats for time and distance calculations. Create a logical daily schedule with travel times and rest periods.", "goals": ["Organize activities into logical daily schedules using the datetime tool", "Include travel times between locations with appropriate buffers", "Balance activities with rest periods throughout the day", "Use the calculator tool for precise time and distance calculations"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 1024}, "tools": {"datetime": "datetime", "travelSearch": "travelSearch", "calculator": "calculator"}}, "budgetingAgent": {"id": "budgetingAgent", "name": "Budgeting Agent", "description": "Provides cost analysis and spending guidance", "role": "You are a budget analyst. You receive all previous results and calculate total costs. Use [TOOL: travelSearch({\"query\": \"cost of [item] in [destination]\"})] for current pricing and [TOOL: calculator({\"operation\": \"multiply\", \"a\": 100, \"b\": 7})] for calculations. Provide detailed cost breakdowns with tiered options.", "goals": ["Calculate total expected costs using current pricing from search results", "Provide tiered options (luxury/mid/economy) with specific price points", "Break down costs by category with detailed estimates based on search data", "Use the calculator tool to perform precise calculations on pricing data"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 1024}, "tools": {"travelSearch": "travelSearch", "calculator": "calculator"}}}, "team": {"vacationTeam": {"name": "Vacation Planning Team", "description": "A team that creates comprehensive vacation plans", "agents": ["goalPlanner", "destination<PERSON><PERSON><PERSON><PERSON>", "accommodationAgent", "<PERSON><PERSON>lanner", "itineraryCoordinator", "budgetingAgent"], "jobs": {"planGoals": {"description": "Convert vacation idea into structured plan", "agent": "goalPlanner", "inputs": {"vacationIdea": "Relaxing beach vacation in Miami with some cultural experiences", "preferences": "Beachfront accommodations, local cuisine, some water activities, cultural sites", "travelDates": "Aug 15-22, 2025", "budget": "Mid-range ($3000-5000)", "weatherPreferences": "Warm and sunny", "culturalInterests": "Local history, cuisine, architecture", "lodgingPreferences": "Beachfront resort with pool", "interests": "Snorkeling, local markets, historical sites, beach relaxation", "budgetLevel": "Mid-range with some splurges"}}, "researchDestinations": {"description": "Research and compare potential destinations", "agent": "destination<PERSON><PERSON><PERSON><PERSON>", "inputs": {"parsedVacationDetails": "{{planGoals.output}}"}}, "findAccommodations": {"description": "Find and evaluate lodging options", "agent": "accommodationAgent", "inputs": {"parsedVacationDetails": "{{planGoals.output}}", "destinationResults": "{{researchDestinations.output}}"}}, "planActivities": {"description": "Curate experiences based on interests", "agent": "<PERSON><PERSON>lanner", "inputs": {"parsedVacationDetails": "{{planGoals.output}}", "destinationResults": "{{researchDestinations.output}}"}}, "createBudget": {"description": "Analyze costs and provide spending guidance", "agent": "budgetingAgent", "inputs": {"parsedVacationDetails": "{{planGoals.output}}", "destinationResults": "{{researchDestinations.output}}", "accommodationsResults": "{{findAccommodations.output}}", "activitiesResults": "{{planActivities.output}}"}}, "createItinerary": {"description": "Create coherent daily schedule", "agent": "itineraryCoordinator", "inputs": {"parsedVacationDetails": "{{planGoals.output}}", "destinationResults": "{{researchDestinations.output}}", "accommodationsResults": "{{findAccommodations.output}}", "activitiesResults": "{{planActivities.output}}", "budgetSummary": "{{createBudget.output}}"}}}, "workflow": ["planGoals", "researchDestinations", "findAccommodations", "planActivities", "createBudget", "createItinerary"]}}, "brief": {"beach-vacation-001": {"title": "Relaxing Beach Vacation in Miami, FL", "overview": "Plan a 7-day relaxing beach vacation in Miami, FL for a couple", "background": "<PERSON><PERSON><PERSON> in their 30s looking for a mix of relaxation and cultural experiences", "objective": "Create a complete 7-day itinerary with accommodations, dining, and activities", "vacationIdea": "Relaxing beach vacation in Miami with some cultural experiences", "preferences": "Beachfront accommodations, local cuisine, some water activities, cultural sites", "travelDates": "Aug 15-22, 2025", "budget": "Mid-range ($3000-5000)", "weatherPreferences": "Warm and sunny", "culturalInterests": "Local history, cuisine, architecture", "lodgingPreferences": "Beachfront resort with pool", "interests": "Snorkeling, local markets, historical sites, beach relaxation", "budgetLevel": "Mid-range with some splurges", "topic": "Beach Vacation"}}, "assignments": {"beach-vacation-001": {"assignedTo": "vacationTeam", "type": "team"}}}