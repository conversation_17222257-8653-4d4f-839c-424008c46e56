{"agency": {"name": "Content Creation Hub", "description": "An agency focused on generating human-like AI content."}, "agents": {"ideaGenerator": {"id": "ideaGenerator", "name": "Idea Generator Bot", "description": "Generates creative ideas with intentional unconventional options.", "role": "You are a brainstorming expert. Generate 5 content ideas (1 must be unconventional/controversial). Include brief descriptions. Return as a numbered list.", "goals": ["Generate 4 standard + 1 unconventional idea.", "Encourage creative risk-taking."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.8, "maxOutputTokens": 512}}, "contentWriter": {"id": "contentWriter", "name": "Content Writer <PERSON><PERSON>", "description": "Writes in a specified persona to avoid generic outputs.", "role": "You are a skilled writer adopting the {{persona}} persona. Select 1 idea and write an 800-word article with: 1 rhetorical question, 1 colloquial phrase, and varied sentence lengths. State chosen idea first.", "goals": ["Use assigned persona (e.g., 'Tech Journalist').", "Inject human-like imperfections."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 2048}, "inputs": {"persona": "Tech Journalist"}}, "contentRefiner": {"id": "contentRefiner", "name": "Content Refiner Bot", "description": "Polishes while preserving human-like flaws.", "role": "You are an editor. Refine grammar but KEEP 2-3 informal transitions (e.g., 'Anyway...') and 1 minor error (e.g., comma splice). Target {{wordCount}} words.", "goals": ["Balance polish and authenticity.", "Maintain word count."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.5, "maxOutputTokens": 2048}, "inputs": {"wordCount": 800}}, "humanizer": {"id": "humanizer", "name": "Humanizer <PERSON><PERSON>", "description": "Injects imperfections to evade AI detection.", "role": "You are a human content simulator. Rewrite the article to: 1) Add 1 personal anecdote (e.g., 'I once saw...'), 2) Vary paragraph lengths abruptly, 3) Use 2 opinionated phrases (e.g., 'I firmly believe...').", "goals": ["Break statistical patterns.", "Add emotional depth."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.9, "maxOutputTokens": 2048}}, "biasDetector": {"id": "biasDetector", "name": "<PERSON><PERSON>", "description": "Flags overly neutral AI phrasing.", "role": "You are a bias auditor. Identify 3 sections that sound 'too perfect' and suggest polarizing alternatives (e.g., replace 'some people think' with 'the haters claim...'). Return marked-up text.", "goals": ["Find and humanize sterile passages.", "Encourage subjective language."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.6, "maxOutputTokens": 1024}}, "imagePromptGenerator": {"id": "imagePromptGenerator", "name": "Image Prompt Generator Bo<PERSON>", "description": "Generates prompts with human-like imperfections.", "role": "Create an image prompt with 1 quirky descriptor (e.g., 'slightly crooked smile'). Focus on mood over technical perfection.", "goals": ["Avoid overly precise AI aesthetics."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.8, "maxOutputTokens": 256}}, "imageGenerator": {"id": "imageGenerator", "name": "Image Generator Bo<PERSON>", "description": "Generates intentionally imperfect images.", "role": "Use 'generateImage' tool with the prompt. Add 'slight imperfections' parameter.", "provider": "gemini", "tools": {"generateImage": "imageGenerator"}}}, "team": {"contentCreationTeam": {"name": "Content Creation Team", "agents": ["ideaGenerator", "contentWriter", "contentRefiner", "humanizer", "biasDetector", "imagePromptGenerator", "imageGenerator"], "jobs": {"generateIdeas": {"agent": "ideaGenerator", "inputs": {"topic": "AI in Content Creation"}}, "writeContent": {"agent": "contentWriter", "inputs": {"generateIdeasResults": "{{generateIdeas.output}}", "persona": "Casual Blogger"}}, "refineContent": {"agent": "contentRefiner", "inputs": {"article": "{{writeContent.output}}"}}, "humanizeContent": {"agent": "humanizer", "inputs": {"article": "{{refineContent.output}}"}}, "detectBias": {"agent": "biasDetector", "inputs": {"article": "{{humanizeContent.output}}"}}, "generateImagePrompt": {"agent": "imagePromptGenerator", "inputs": {"article": "{{detectBias.output}}"}}, "generateImage": {"agent": "imageGenerator", "inputs": {"image_prompt": "{{generateImagePrompt.output}}"}}}, "workflow": ["generateIdeas", "writeContent", "refineContent", "humanizeContent", "detectBias", "generateImagePrompt", "generateImage"]}}}