import { EventEmitter } from './Agent.js';

/**
 * Advanced Tool Handler with multiple syntax support and validation
 */
export class AdvancedToolHandler {
  constructor(config = {}) {
    this.events = new EventEmitter();
    this.retryAttempts = config.retryAttempts || 3;
    this.retryDelay = config.retryDelay || 1000;
    this.fallbackEnabled = config.fallbackEnabled || true;
  }

  /**
   * Handle tool calls with multiple syntax support
   */
  async handleToolCalls(responseText, tools, agent) {
    const toolCalls = this._extractToolCalls(responseText);
    let processedResponse = responseText;

    for (const call of toolCalls) {
      try {
        const tool = tools[call.toolName];
        if (!tool) {
          throw new Error(`Tool '${call.toolName}' not found`);
        }

        this.events.emit('toolCallStarted', {
          agentId: agent.id,
          toolName: call.toolName,
          params: call.params
        });

        const result = await this._executeWithRetry(tool, call.params);
        
        const formattedResult = this._formatToolResult(call.toolName, call.params, result);
        processedResponse = processedResponse.replace(call.fullMatch, formattedResult);

        this.events.emit('toolCallCompleted', {
          agentId: agent.id,
          toolName: call.toolName,
          params: call.params,
          result
        });

      } catch (error) {
        this.events.emit('toolCallFailed', {
          agentId: agent.id,
          toolName: call.toolName,
          params: call.params,
          error: error.message
        });

        const errorMessage = this.fallbackEnabled 
          ? `Tool ${call.toolName} unavailable, continuing without it.`
          : `Error: ${error.message}`;
        
        processedResponse = processedResponse.replace(call.fullMatch, errorMessage);
      }
    }

    return processedResponse;
  }

  /**
   * Extract tool calls from response text (supports multiple formats)
   */
  _extractToolCalls(responseText) {
    const toolCalls = [];

    // Format 1: [TOOL: toolName(params)]
    const bracketRegex = /\[TOOL:\s*(\w+)\(([^)]*)\)\]/g;
    let match;
    while ((match = bracketRegex.exec(responseText)) !== null) {
      toolCalls.push({
        toolName: match[1],
        params: this._parseParams(match[2]),
        fullMatch: match[0],
        format: 'bracket'
      });
    }

    // Format 2: JSON function calling
    const jsonRegex = /\{"tool":\s*"(\w+)",\s*"parameters":\s*({[^}]+})\}/g;
    while ((match = jsonRegex.exec(responseText)) !== null) {
      try {
        const params = JSON.parse(match[2]);
        toolCalls.push({
          toolName: match[1],
          params,
          fullMatch: match[0],
          format: 'json'
        });
      } catch (e) {
        console.warn('Failed to parse JSON tool call:', match[0]);
      }
    }

    return toolCalls;
  }

  /**
   * Parse parameters from string format
   */
  _parseParams(paramString) {
    if (!paramString.trim()) return {};
    
    try {
      // Try JSON parsing first
      return JSON.parse(paramString.replace(/'/g, '"'));
    } catch (e) {
      // Fallback to simple string parameter
      return { query: paramString.replace(/['"]/g, '') };
    }
  }

  /**
   * Execute tool with retry logic
   */
  async _executeWithRetry(tool, params, attempt = 1) {
    try {
      return await tool.execute(params);
    } catch (error) {
      if (attempt < this.retryAttempts) {
        console.log(`Tool execution failed, retrying (${attempt}/${this.retryAttempts})...`);
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
        return this._executeWithRetry(tool, params, attempt + 1);
      }
      throw error;
    }
  }

  /**
   * Format tool result for injection back into response
   */
  _formatToolResult(toolName, params, result) {
    if (typeof result === 'string') {
      return result;
    }
    return `Tool ${toolName} executed successfully. Result: ${JSON.stringify(result)}`;
  }
}