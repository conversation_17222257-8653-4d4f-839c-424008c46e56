import { GoogleGenAI, Modality } from "@google/genai";
import * as fs from "node:fs";
import * as path from "node:path";
import dotenv from 'dotenv';

dotenv.config();

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const DEFAULT_OUTPUT_DIR = "generated_images";

if (!GEMINI_API_KEY) {
  throw new Error('GEMINI_API_KEY environment variable is not set');
}

/**
 * Gemini Image Generator class for creating images using Google's Gemini API
 */
class GeminiImageGenerator {
  /**
   * Create a new Gemini Image Generator
   * @param {string} [apiKey=GEMINI_API_KEY] - Gemini API key
   * @param {string} [modelName="gemini-2.0-flash-preview-image-generation"] - Model name for image generation
   * @throws {Error} - If API key is not provided
   */
  constructor(apiKey = GEMINI_API_KEY, modelName = "gemini-2.0-flash-preview-image-generation") {
    this.apiKey = apiKey;
    this.modelName = modelName;
    this.gemini = new GoogleGenAI({ apiKey: this.apiKey });
  }

  /**
   * Generates an image from a text prompt
   * @param {string} prompt - The image generation prompt
   * @param {string} [outputFilename="generated_image.png"] - Output filename
   * @param {string} [outputDir="generated_images"] - Output directory
   * @returns {Promise<{imagePath: string, textResponse?: string}>} Result object
   */
  async generateImage(prompt, outputFilename = "generated_image.png", outputDir = DEFAULT_OUTPUT_DIR) {
    try {
      if (!prompt || typeof prompt !== 'string') {
        throw new Error("Prompt must be a non-empty string");
      }

      console.log(`Generating image for prompt: "${prompt}"`);
      
      const response = await this.gemini.models.generateContent({
        model: this.modelName,
        contents: prompt,
        config: {
          responseModalities: [Modality.TEXT, Modality.IMAGE] // Request both
        }
      });

      // Create output directory if needed
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      const outputPath = path.join(outputDir, outputFilename);
      const result = { imagePath: outputPath };

      // Process response parts
      for (const part of response.candidates[0].content.parts) {
        if (part.inlineData?.mimeType?.startsWith('image/')) {
          const buffer = Buffer.from(part.inlineData.data, "base64");
          fs.writeFileSync(outputPath, buffer);
          console.log(`Image saved to: ${outputPath}`);
        } else if (part.text) {
          result.textResponse = part.text;
          console.log("Text response:", part.text);
        }
      }

      if (!fs.existsSync(outputPath)) {
        throw new Error("No image data found in response");
      }

      return result;

    } catch (error) {
      console.error("Image generation error:", error.message);
      
      if (error.message.includes("API key not valid")) {
        throw new Error("Invalid API key");
      }
      if (error.message.includes("quota")) {
        throw new Error("API quota exceeded");
      }
      
      throw error;
    }
  }
}

/**
 * Standalone image generation
 * @param {string} prompt - Image prompt
 * @param {string} [filename] - Output filename
 * @param {string} [outputDir] - Output directory
 * @returns {Promise<Object>} - Object containing imagePath and textResponse
 * @throws {Error} - If no prompt provided or image generation fails
 */
async function generateImageFromPrompt(prompt, filename, outputDir) {
  if (!prompt) {
    throw new Error("No prompt provided");
  }

  const generator = new GeminiImageGenerator();
  const { imagePath, textResponse } = await generator.generateImage(
    prompt, 
    filename,
    outputDir
  );

  return {
    imagePath: path.resolve(imagePath),
    textResponse
  };
}

// Command line execution
if (import.meta.url === `file://${process.argv[1]}`) {
  if (process.argv.length < 3) {
    console.error("Usage: node ig.js \"your prompt\" [filename] [outputDir]");
    console.error("Example: node ig.js \"a cat astronaut\" cat.png ./output");
    process.exit(1);
  }

  const prompt = process.argv[2];
  const filename = process.argv[3];
  const outputDir = process.argv[4];

  generateImageFromPrompt(prompt, filename, outputDir)
    .then(({ imagePath, textResponse }) => {
      console.log("Successfully generated image:", imagePath);
      if (textResponse) {
        console.log("Additional text response:", textResponse);
      }
    })
    .catch(err => {
      console.error("Error:", err.message);
      process.exit(1);
    });
}

export { GeminiImageGenerator, generateImageFromPrompt };