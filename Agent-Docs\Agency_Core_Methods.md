# Agency Core Methods

## constructor(config)

**Purpose**: Initializes a new Agency instance with the provided configuration.

**Parameters**:
- `config`: Object containing agency configuration
  - `name`: Agency name
  - `description`: Agency description
  - `memoryConfig`: Optional configuration for memory management

**Behavior**: 
The constructor initializes both core and enhanced components of the Agency:

**Core components**:
- `agents`: Object storing all registered agents
- `team`: Object storing all registered team
- `brief`: Object storing job brief
- `activeJobs`: Object storing currently active jobs

**Enhanced components**:
- `events`: EventEmitter instance for agency-wide event communication
- `globalMemory`: MemoryManager instance for agency-wide memory storage
- `memoryScopes`: Object storing different memory scopes, with 'global' scope initialized by default
- `jobContexts`: Object storing context data for jobs
- `workflows`: Object storing workflow definitions and states
- `errorHandlers`: Object storing error handlers for jobs
- `workflowErrorHandlers`: Object storing error handlers for workflows
- `jobSchemas`: Object storing schemas for job inputs and outputs
- `memoryAccessControl`: Object storing memory access control information

**Example**:
```javascript
const agency = new Agency({
  name: 'Marketing Agency',
  description: 'Agency for handling marketing tasks',
  memoryConfig: {
    // Memory manager configuration
  }
});
```

## addAgent(id, agent)

**Purpose**: Registers an agent with the agency.

**Parameters**:
- `id`: String identifier for the agent
- `agent`: Agent instance to add

**Behavior**: 
1. Stores the agent in the agents collection using the provided ID as the key
2. Sets up event subscriptions to relay agent events through the agency's event system:
   - 'statusChanged': Relayed as 'agent:statusChanged'
   - 'runCompleted': Relayed as 'agent:runCompleted'
   - 'runError': Relayed as 'agent:runError'
3. Returns the agency instance for method chaining

**Returns**: The agency instance (this) for method chaining.

**Example**:
```javascript
const contentWriter = new Agent({ /* agent config */ });
agency.addAgent('writer', contentWriter);
```

## addTeam(id, team)

**Purpose**: Registers a team with the agency.

**Parameters**:
- `id`: String identifier for the team
- `team`: Team instance to add

**Behavior**: 
1. Stores the team in the team collection using the provided ID as the key
2. Returns the agency instance for method chaining

**Returns**: The agency instance (this) for method chaining.

**Example**:
```javascript
const marketingTeam = new Team({ /* team config */ });
agency.addTeam('marketing', marketingTeam);
```

## getJobsByStatus(status)

**Purpose**: Gets all jobs with a specific status.

**Parameters**:
- `status`: Status to filter jobs by (e.g., 'assigned', 'in_progress', 'completed', 'failed')

**Behavior**: 
1. Filters the activeJobs collection to find jobs with the specified status
2. Returns an array of matching job objects

**Returns**: Array of jobs with the specified status.

**Example**:
```javascript
const failedJobs = agency.getJobsByStatus('failed');
```

## getJob(jobId)

**Purpose**: Gets a job by ID.

**Parameters**:
- `jobId`: String identifier for the job

**Behavior**: 
1. Retrieves the job with the specified ID from the activeJobs collection
2. Returns the job object or undefined if not found

**Returns**: The job object or undefined if not found.

**Example**:
```javascript
const job = agency.getJob('job-123');
```
