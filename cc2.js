// cc2.js

import { AgentFactory } from './AgentFactory.js';
import { TeamFactory } from './TeamFactory.js';
import { AgencyFactory } from './AgencyFactory.js';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import fs from 'fs/promises';
import { GeminiImageGenerator } from './ig.js';

// Load environment variables from .env file
dotenv.config();

async function main() {
  try {
    const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

    if (!GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is not set');
    }

    // Create agent factory with API keys and register the tool
    const agentFactory = new AgentFactory({
      defaultProvider: 'gemini',
      apiKeys: {
        gemini: GEMINI_API_KEY
      }
    });

    // Register the image generator tool
    agentFactory.registerTool('imageGenerator', new GeminiImageGenerator());

    // Create team factory
    const teamFactory = new TeamFactory({ agentFactory });

    // Create agency factory
    const agencyFactory = new AgencyFactory({
      teamFactory,
      agentFactory
    });

    // Get the directory name
    const __dirname = path.dirname(fileURLToPath(import.meta.url));

    // Path to the config file
    const configPath = path.join(__dirname, 'cc2.json');

    // Load content creation agency configuration
    const agency = await agencyFactory.loadAgencyFromFile(configPath);

    console.log('Agency loaded successfully:');
    console.log('- name:', agency.name);
    console.log('- team:', Object.keys(agency.team)); // This will likely be empty if no teams are defined
    console.log('- brief:', Object.keys(agency.brief));

    const briefIdToExecute = 'blog-post-008';
    console.log(`Executing brief: ${briefIdToExecute}`);

    // Extract inputs from the brief for the blog post
    const brief = agency.brief[briefIdToExecute];

    // Assign the job to the content creation team
    console.log('Assigning job "blog-post-008" to "spaceExplorationTeam" (type: team)...');
    agency.assignJob('blog-post-008', 'spaceExplorationTeam', 'team');

    // Execute the content creation workflow with the initial brief inputs
    console.log('Executing job...');
    console.log('Using initial inputs for the workflow:');
    console.log('- topic:', brief.topic);
    console.log('- brief:', brief.overview);

    const results = await agency.execute('blog-post-008', {
      topic: brief.topic,
      brief: brief.overview
    });

    console.log('Job execution completed. Results received.');

    console.log('\n=== CONTENT CREATION RESULTS ===\n');

    console.log('Results object contains keys:', Object.keys(results));

    // Display and save results
    let markdownContent = '# CONTENT CREATION RESULTS\n\n';
    markdownContent += `Results object contains keys: [ ${Object.keys(results).join(', ')} ]\n\n`;

    const team = agency.team['spaceExplorationTeam'];
    for (const jobName of team.workflow) {
        if (results[jobName]) {
            const resultText = typeof results[jobName] === 'object' ? JSON.stringify(results[jobName], null, 2) : results[jobName];
            console.log(`${jobName.replace(/([A-Z])/g, ' $1').trim()} (length: ${resultText.length} characters):`);
            console.log(resultText);
            console.log('\n');
            markdownContent += `## ${jobName.replace(/([A-Z])/g, ' $1').trim()} (length: ${resultText.length} characters):\n${resultText}\n\n`;
        } else {
            console.log(`WARNING: No ${jobName} results found!`);
            markdownContent += `## ${jobName.replace(/([A-Z])/g, ' $1').trim()}:\nWARNING: No results found for this step.\n\n`;
        }
    }

    // Special handling for the image (if it's a path or URL)
    if (results.generateImage && results.generateImage.imagePath) {
        markdownContent += `## Generated Image:\n![Generated Image](${results.generateImage.imagePath})\n\n`;
    } else if (results.generateImage) {
        // If imagePath is not available, but some image data is, stringify it.
        markdownContent += `## Generated Image (Text Description):\n${JSON.stringify(results.generateImage, null, 2)}\n\n`;
    }

    const resultsFilePath = path.join(__dirname, 'content-results.md');
    await fs.writeFile(resultsFilePath, markdownContent, 'utf8');
    console.log(`Results saved to ${resultsFilePath}`);

    console.log('Content creation workflow completed successfully!');

  } catch (error) {
    console.error('Error during workflow execution:', error);
    if (error.response && error.response.data) {
      console.error('API Error Details:', error.response.data);
    }
  }
}

main();
