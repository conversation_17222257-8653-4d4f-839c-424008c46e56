// vacation_planner.js

import { AgentFactory } from './AgentFactory.js';
import { TeamFactory } from './TeamFactory.js';
import { AgencyFactory } from './AgencyFactory.js';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import fs from 'fs/promises';
import { webSearch } from './search_tool.js';
import { dateTimeTool } from './datetime.js';
import { calculatorTool as calculator } from './calculator.js';

// Load environment variables from a .env file.
dotenv.config();

async function main() {
  try {
    const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

    if (!GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is not set');
    }

    // Initialize the factories with the Gemini API key.
    const agentFactory = new AgentFactory({
      defaultProvider: 'gemini',
      apiKeys: {
        gemini: GEMINI_API_KEY
      }
    });

    // Register tools using new Tool class with validation and monitoring
    const travelSearchTool = agentFactory.registerTool('travelSearch', webSearch, {
      description: 'Search for travel-related information including destinations, accommodations, and activities',
      isAsync: true,
      timeout: 15000
    });

    const datetimeTool = agentFactory.registerTool('datetime', dateTimeTool, {
      description: 'Get current date/time information and perform date calculations',
      isAsync: false,
      timeout: 5000
    });

    const calculatorToolInstance = agentFactory.registerTool('calculator', calculator, {
      description: 'Perform mathematical calculations for budgeting and planning',
      isAsync: false,
      timeout: 5000
    });

    // Add validators and monitors
    travelSearchTool.addValidator((params) => {
      if (!params.query || params.query.length < 3) {
        throw new Error('Search query must be at least 3 characters long');
      }
    });

    travelSearchTool.addMonitor((toolName, params, duration, success, result, error) => {
      console.log(`Tool ${toolName} executed in ${duration}ms, success: ${success}`);
      if (error) console.log(`Error: ${error.message}`);
    });

    // Create the team and agency factories, passing the agentFactory instance.
    const teamFactory = new TeamFactory({ agentFactory });
    const agencyFactory = new AgencyFactory({
      teamFactory,
      agentFactory
    });

    // Get the directory name of the current module.
    const __dirname = path.dirname(fileURLToPath(import.meta.url));

    // Define the path to the vacation planner configuration file.
    // Make sure your JSON file is saved as `vp3.json` in the same directory.
    const configPath = path.join(__dirname, 'vp3.json');

    // Load the vacation planner agency configuration from the JSON file.
    const agency = await agencyFactory.loadAgencyFromFile(configPath);

    console.log('Vacation Planner Agency loaded successfully:');
    console.log('- name:', agency.name);
    console.log('- team:', Object.keys(agency.team));
    console.log('- brief:', Object.keys(agency.brief));

    console.log('Executing brief: beach-vacation-001');

    const brief = agency.brief['beach-vacation-001'];

    // Assign the job to the team
    console.log('Assigning job "beach-vacation-001" to "vacationTeam" (type: team)...');
    agency.assignJob('beach-vacation-001', 'vacationTeam', 'team');

    // Execute the workflow
    console.log('Executing job...');
    console.log('Using initial inputs for the workflow:');
    console.log('- brief:', brief);

    const results = await agency.execute('beach-vacation-001', brief);

    console.log('Job execution completed. Results received.');

    console.log('\n=== VACATION PLANNING RESULTS ===\n');

    console.log('Results object contains keys:', Object.keys(results));

    // Display the results for each step in the workflow.
    let markdownContent = '# VACATION PLANNING RESULTS\n\n';
    markdownContent += `Results object contains keys: [ ${Object.keys(results).join(', ')} ]\n\n`;

    const team = agency.team['vacationTeam'];
    for (const jobName of team.workflow) {
      if (results[jobName]) {
        const resultText = typeof results[jobName] === 'object' ? JSON.stringify(results[jobName], null, 2) : results[jobName];
        console.log(`${jobName.replace(/([A-Z])/g, ' $1').trim()} (length: ${resultText.length} characters):`);
        console.log(resultText);
        console.log('\n');
        markdownContent += `## ${jobName.replace(/([A-Z])/g, ' $1').trim()} (length: ${resultText.length} characters):\n${resultText}\n\n`;
      } else {
        console.log(`WARNING: No ${jobName} results found!`);
        markdownContent += `## ${jobName.replace(/([A-Z])/g, ' $1').trim()}:\nWARNING: No results found for this step.\n\n`;
      }
    }

    // Save the results to a Markdown file.
    const resultsFilePath = path.join(__dirname, 'vacation-results.md');
    await fs.writeFile(resultsFilePath, markdownContent, 'utf8');
    console.log(`Results saved to ${resultsFilePath}`);

    console.log('Vacation planning workflow completed successfully!');

  } catch (error) {
    console.error('Error during workflow execution:', error);
    if (error.response && error.response.data) {
      console.error('API Error Details:', error.response.data);
        }
  }
}

// Execute the main function.
main();
