// search_tool.js

// Import the search functionality from the search_tool directory
import search from './search_tool/search.js';

/**
 * Perform a web search using the search tool
 * @param {string|Object} query - The search query (string) or params object with query property
 * @returns {Promise<Array>} - An array of search results
 */
export async function webSearch(query) {
  // Handle both string and object parameter formats
  const searchQuery = typeof query === 'string' ? query : query.query;

  console.log(`🔍 Performing web search for: ${searchQuery}`);

  try {
    return await search(searchQuery);
  } catch (error) {
    console.error('Error performing web search:', error);
    return [{
      title: 'Search Error',
      snippet: `Failed to perform search: ${error.message}`,
      url: '#'
    }];
  }
}

// Export the search function as default
export default {
  webSearch
};
