# Agency Memory Management Methods

## createMemoryScope(scopeId, config)

**Purpose**: Creates a new memory scope for sharing data.

**Parameters**:
- `scopeId`: String identifier for the memory scope
- `config`: Optional configuration for the memory manager

**Behavior**: 
1. Checks if a memory scope with the provided ID already exists
2. If it exists, throws an error
3. Creates a new MemoryManager instance with the provided configuration
4. Stores the memory scope in the memoryScopes collection using the provided ID as the key

**Returns**: The created memory scope (MemoryManager instance).

**Example**:
```javascript
const teamMemory = agency.createMemoryScope('marketing-team', {
  // Memory manager configuration
});

// Now the team can store and retrieve data
teamMemory.remember('campaign-ideas', ['Idea 1', 'Idea 2']);
```

## getMemoryScope(scopeId)

**Purpose**: Retrieves a memory scope by ID.

**Parameters**:
- `scopeId`: String identifier of the memory scope to retrieve

**Behavior**: 
1. Retrieves the memory scope with the specified ID from the memoryScopes collection
2. If the scope doesn't exist, throws an error

**Returns**: The memory scope (MemoryManager instance).

**Example**:
```javascript
try {
  const teamMemory = agency.getMemoryScope('marketing-team');
  const ideas = teamMemory.recall('campaign-ideas');
} catch (error) {
  console.error('Memory scope not found:', error.message);
}
```

## shareMemoryBetween(sourceId, targetId, keys, accessMode)

**Purpose**: Shares memory between scopes with access control.

**Parameters**:
- `sourceId`: String identifier of the source memory scope
- `targetId`: String identifier of the target memory scope
- `keys`: Array of keys to share (empty array shares all keys)
- `accessMode`: Access mode ('read-only' or 'read-write', defaults to 'read-write')

**Behavior**: 
1. Retrieves both source and target memory scopes
2. If keys array is empty, shares all keys from the source scope
3. For each key to share:
   - Retrieves the value from the source scope
   - If the value exists, stores it in the target scope
   - Records access control information
   - If access mode is 'read-only', sets up a proxy to prevent writes to the shared key
4. Emits a 'memory:shared' event with sharing details

**Example**:
```javascript
// Share specific keys with read-only access
agency.shareMemoryBetween(
  'global', 
  'team-scope', 
  ['company-guidelines', 'brand-voice'], 
  'read-only'
);

// Share all keys with read-write access
agency.shareMemoryBetween('research-scope', 'writing-scope');
```

## getMemoryAccessInfo(scopeId, key)

**Purpose**: Gets memory access control information.

**Parameters**:
- `scopeId`: String identifier of the memory scope
- `key`: String key to get access information for

**Behavior**: 
1. Checks if access control information exists for the specified scope and key
2. If it doesn't exist, returns null
3. Otherwise, returns the access control information

**Returns**: Access control information object or null if not found. The object contains:
- `sourceScope`: ID of the source memory scope
- `accessMode`: Access mode ('read-only' or 'read-write')
- `sharedAt`: Date when the memory was shared

**Example**:
```javascript
const accessInfo = agency.getMemoryAccessInfo('team-scope', 'company-guidelines');
if (accessInfo) {
  console.log(`Shared from ${accessInfo.sourceScope} with ${accessInfo.accessMode} access`);
}
```
