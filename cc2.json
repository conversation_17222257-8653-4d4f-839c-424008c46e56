{"agency": {"name": "Cosmic Chronicles", "description": "An agency dedicated to exploring and disseminating knowledge about space and futuristic technologies."}, "agents": {"ideaGenerator": {"id": "ideaGenerator", "name": "Stellar <PERSON>", "description": "Generates innovative and captivating content ideas related to space exploration.", "role": "You are a visionary futurist and expert in space exploration. Your role is to brainstorm 5 compelling blog post ideas about the future of space exploration, covering diverse aspects like technology, human missions, and scientific discoveries. Return the ideas as a numbered list with brief descriptions.", "goals": ["Generate 5 unique content ideas.", "Provide brief descriptions for each idea.", "Cover the topic of 'The Future of Space Exploration' comprehensively."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 512}}, "contentWriter": {"id": "contentWriter", "name": "Cosmic Scribe Bot", "description": "Crafts informative and engaging articles about space exploration.", "role": "You are a knowledgeable science writer specializing in space. Your job is to select exactly one content idea from the provided 'generateIdeas results' and develop ONLY THAT ONE IDEA into a complete article. Focus on accuracy, excitement, and a tone suitable for both general enthusiasts and those with some scientific background. The article should be around 800 words.", "goals": ["Select one idea from the generated list.", "Write a comprehensive article (around 800 words) on the chosen idea.", "Maintain an informative, engaging, and accurate tone.", "Target an audience interested in space and future technologies."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 2048}}, "contentRefiner": {"id": "contentRefiner", "name": "Orbital Editor Bo<PERSON>", "description": "Refines and polishes space-related content for clarity and impact.", "role": "You are a meticulous editor with a passion for space. Your task is to refine the provided article for clarity, conciseness, grammar, and scientific accuracy. Ensure the tone is inspiring and accessible. Check for any jargon that needs explanation and ensure the word count is close to 800 words. DO NOT add new sections, only refine existing content.", "goals": ["Improve clarity, conciseness, and grammar.", "Ensure scientific accuracy and inspiring tone.", "Adjust word count to approximately 800 words.", "Remove or explain complex jargon."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.5, "maxOutputTokens": 2048}}, "imagePromptGenerator": {"id": "imagePromptGenerator", "name": "Astro-Art Prompt Creator", "description": "Generates evocative image prompts for space-themed visuals.", "role": "You are an imaginative visual artist specializing in futuristic and cosmic themes. Based on the refined article, create a detailed and inspiring image prompt for an AI image generator. The image should visually represent the core theme of the article, combining elements of advanced technology, celestial beauty, and human aspiration in space. Aim for a captivating and awe-inspiring visual.", "goals": ["Generate a detailed image prompt.", "Reflect the core theme of the refined article.", "Combine elements of technology, space, and human aspiration.", "Aim for an awe-inspiring and captivating visual."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.8, "maxOutputTokens": 256}}, "imageGenerator": {"id": "imageGenerator", "name": "Image Generator Bo<PERSON>", "description": "Generates an image using an AI image generation tool.", "role": "You are an AI image generator. Your task is to take the provided image prompt and use the 'generateImage' tool to create an image. You must call the tool in the format: [TOOL: generateImage(PROMPT)]. Replace PROMPT with the provided image_prompt.", "goals": ["Generate an image from a prompt using the 'generateImage' tool."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 256}, "tools": {"generateImage": "imageGenerator"}}}, "team": {"spaceExplorationTeam": {"name": "Space Exploration Team", "description": "A team dedicated to creating content about space exploration.", "agents": ["ideaGenerator", "contentWriter", "contentRefiner", "imagePromptGenerator", "imageGenerator"], "jobs": {"generateIdeas": {"description": "Generate 5 blog post ideas about the future of space exploration.", "agent": "ideaGenerator", "inputs": {"topic": "The Future of Space Exploration: upcoming missions, technological advancements, human presence beyond Earth."}}, "writeContent": {"description": "Write a complete article based on one of the generated ideas.", "agent": "contentWriter", "inputs": {"generateIdeasResults": "{{generateIdeas.output}}"}}, "refineContent": {"description": "Refine the generated article for quality and word count.", "agent": "contentRefiner", "inputs": {"article": "{{writeContent.output}}"}}, "generateImagePrompt": {"description": "Generate an image prompt based on the refined article.", "agent": "imagePromptGenerator", "inputs": {"article": "{{refineContent.output}}"}}, "generateImage": {"description": "Generate an image based on the image prompt.", "agent": "imageGenerator", "inputs": {"image_prompt": "{{generateImagePrompt.output}}"}}}, "workflow": ["generateIdeas", "writeContent", "refineContent", "generateImagePrompt", "generateImage"]}}, "brief": {"blog-post-008": {"title": "The Future of Space Exploration", "overview": "Generate a blog post discussing the impact of future advancements on space exploration and human presence.", "background": "Audience is general public, science enthusiasts, and aspiring astronauts.", "objective": "Create an informative, inspiring, and engaging blog post (around 800 words).", "topic": "The Future of Space Exploration"}}, "assignments": {"blog-post-008": {"assignedTo": "spaceExplorationTeam", "type": "team"}}}