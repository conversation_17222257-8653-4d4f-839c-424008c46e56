# Agency Job Management Methods

## createBrief(jobId, briefData)

**Purpose**: Creates a brief for a job.

**Parameters**:
- `jobId`: String identifier for the job
- `briefData`: Object containing brief information

**Behavior**: 
1. Creates a brief object with:
   - `jobId`: The provided job ID
   - `createdAt`: Current date/time
   - All properties from the provided briefData object
2. Stores the brief in the brief collection using the job ID as the key
3. Emits a 'brief:created' event with the job ID and brief

**Returns**: The created brief object.

**Example**:
```javascript
const brief = agency.createBrief('content-creation-123', {
  title: 'Create Blog Content',
  overview: 'Create engaging blog content for our website',
  background: 'We are a tech company focusing on AI solutions',
  objective: 'Increase website traffic and engagement',
  targetAudience: 'Tech professionals and business leaders',
  deliverables: 'Five 1500-word blog posts on AI topics'
});
```

## createJobContext(jobId, initialContext)

**Purpose**: Creates a job context for sharing data between agents working on the same job.

**Parameters**:
- `jobId`: String identifier for the job
- `initialContext`: Optional initial context data (defaults to empty object)

**Behavior**: 
1. Creates a job context object with:
   - All properties from the provided initialContext object
   - `createdAt`: Current date/time
   - `updatedAt`: Current date/time
2. Stores the context in the jobContexts collection using the job ID as the key
3. Emits a 'jobContext:created' event with the job ID and context

**Returns**: The created job context object.

**Example**:
```javascript
const context = agency.createJobContext('content-creation-123', {
  topics: ['AI Ethics', 'Machine Learning Basics', 'AI in Healthcare'],
  wordCount: 1500,
  tone: 'Professional but approachable'
});
```

## updateJobContext(jobId, updates)

**Purpose**: Updates a job context.

**Parameters**:
- `jobId`: String identifier for the job
- `updates`: Object containing context updates

**Behavior**: 
1. Checks if a job context with the provided ID exists
2. If it doesn't exist, throws an error
3. Updates the job context by merging:
   - The existing context
   - The provided updates
   - `updatedAt`: Current date/time
4. Emits a 'jobContext:updated' event with the job ID, updated context, and list of updated keys

**Returns**: The updated job context object.

**Example**:
```javascript
const updatedContext = agency.updateJobContext('content-creation-123', {
  progress: 60,
  completedTopics: ['AI Ethics', 'Machine Learning Basics']
});
```

## getJobContext(jobId)

**Purpose**: Gets a job context by ID.

**Parameters**:
- `jobId`: String identifier for the job

**Behavior**: 
1. Retrieves the job context with the specified ID from the jobContexts collection
2. If the context doesn't exist, throws an error

**Returns**: The job context object.

**Example**:
```javascript
try {
  const context = agency.getJobContext('content-creation-123');
  console.log('Job progress:', context.progress);
} catch (error) {
  console.error('Job context not found:', error.message);
}
```

## assignJob(jobId, assigneeId, assigneeType)

**Purpose**: Assigns a job to an agent or team.

**Parameters**:
- `jobId`: String identifier for the job
- `assigneeId`: String identifier of the agent or team
- `assigneeType`: Type of assignee ('agent' or 'team', defaults to 'agent')

**Behavior**: 
1. Retrieves the brief for the specified job ID
2. If the brief doesn't exist, throws an error
3. Retrieves the assignee (agent or team) based on the assignee type
4. If the assignee doesn't exist, throws an error
5. Creates a job object with:
   - `jobId`: The provided job ID
   - `brief`: The job brief
   - `assigneeId`: The provided assignee ID
   - `assigneeType`: The provided assignee type
   - `status`: 'assigned'
   - `assignedAt`: Current date/time
   - `results`: null
6. Stores the job in the activeJobs collection using the job ID as the key
7. Creates a job context if it doesn't exist
8. Emits a 'job:assigned' event with the job ID, assignee ID, and assignee type

**Returns**: The created job object.

**Example**:
```javascript
const job = agency.assignJob('content-creation-123', 'writer-team', 'team');
```

## execute(jobId, additionalInputs)

**Purpose**: Executes a job.

**Parameters**:
- `jobId`: String identifier for the job
- `additionalInputs`: Optional additional input data for the job (defaults to empty object)

**Behavior**: 
1. Retrieves the job with the specified ID from the activeJobs collection
2. If the job doesn't exist, throws an error
3. Retrieves the assignee (agent or team) based on the job's assignee type
4. Updates the job status to 'in_progress' and sets the start time
5. Extracts inputs from the brief, job context, and additional inputs
6. Validates inputs against the job schema if defined
7. Executes the job:
   - For an agent, formats the brief as a prompt and calls the agent's run method
   - For a team, passes the inputs and context to the team's run method
8. Validates outputs against the job schema if defined
9. Updates the job status to 'completed', sets the completion time, and stores the results
10. Updates the job context with the results
11. Emits a 'job:completed' event with the job ID and results

**Returns**: Promise resolving to the job results.

**Example**:
```javascript
agency.execute('content-creation-123', {
  priority: 'high',
  deadline: '2023-12-31'
})
.then(results => {
  console.log('Job completed with results:', results);
})
.catch(error => {
  console.error('Job execution failed:', error.message);
});
```

## retryJob(jobId, maxRetries, additionalInputs)

**Purpose**: Retries a failed job.

**Parameters**:
- `jobId`: String identifier for the job
- `maxRetries`: Maximum number of retry attempts (defaults to 3)
- `additionalInputs`: Optional additional inputs for the retry (defaults to empty object)

**Behavior**: 
1. Retrieves the job with the specified ID from the activeJobs collection
2. If the job doesn't exist, throws an error
3. If the job status is not 'failed', throws an error
4. Initializes the retry count if not present
5. If the retry count has reached the maximum, throws an error
6. Increments the retry count
7. Resets the job status to 'assigned' and clears the error
8. Emits a 'job:retrying' event with the job ID, retry count, and maximum retries
9. Executes the job again

**Returns**: Promise resolving to the job results.

**Example**:
```javascript
agency.retryJob('content-creation-123', 5, {
  useAlternativeApproach: true
})
.then(results => {
  console.log('Job retry succeeded with results:', results);
})
.catch(error => {
  console.error('Job retry failed:', error.message);
});
```

## cleanupJob(jobId, keepResults)

**Purpose**: Cleans up resources for a completed job.

**Parameters**:
- `jobId`: String identifier for the job
- `keepResults`: Whether to keep job results in global memory (defaults to true)

**Behavior**: 
1. Retrieves the job with the specified ID from the activeJobs collection
2. If the job doesn't exist, returns false
3. If the job status is not 'completed' or 'failed', returns false
4. Stores the results if keepResults is true
5. Removes the job context from the jobContexts collection
6. Removes the job from the activeJobs collection
7. Emits a 'job:cleaned' event with the job ID and keepResults flag
8. If keeping results, stores them in global memory

**Returns**: Boolean indicating success.

**Example**:
```javascript
const success = agency.cleanupJob('content-creation-123', true);
if (success) {
  console.log('Job resources cleaned up successfully');
}
```
