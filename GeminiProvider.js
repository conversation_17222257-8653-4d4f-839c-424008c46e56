import { GoogleGenAI } from '@google/genai';

/**
 * GeminiProvider class for generating content using Google's Gemini API
 */
export class GeminiProvider {
  /**
   * Create a new Gemini provider
   * @param {string} apiKey - Gemini API key
   * @param {string} modelName - Model name to use (default: 'gemini-2.5-flash-lite')
   * @throws {Error} - If API key is not provided
   */
  constructor(apiKey, modelName = 'gemini-2.5-flash-lite') {
    this.genAI = new GoogleGenAI({ apiKey });
    this.modelName = modelName;
  }

  // gemini-2.5-flash-lite

  /**
   * Generate content using Gemini
   * @param {Object} prompt - Formatted prompt for the LLM
   * @param {Object} prompt.contents - Array of content parts
   * @param {number} [prompt.temperature] - Temperature for generation (0.0-1.0)
   * @param {number} [prompt.topP] - Top-p sampling parameter
   * @param {number} [prompt.topK] - Top-k sampling parameter
   * @param {number} [prompt.maxOutputTokens] - Maximum output tokens
   * @param {string} [prompt.systemInstruction] - System instruction
   * @param {Object} [tools={}] - Object containing tool functions (Agent format)
   * @returns {Promise<string>} - LLM response
   * @throws {Error} - If API call fails or quota is exceeded
   */
  async generateContent(prompt, tools = {}) {
    try {
      // Implement retry with backoff logic
      return await this.retryWithBackoff(async () => {
        const request = {
          model: this.modelName,
          contents: prompt.contents,
          config: {
            temperature: prompt.temperature || 0.7,
            topP: prompt.topP || 0.95,
            topK: prompt.topK || 40,
            maxOutputTokens: prompt.maxOutputTokens || 1024,
            systemInstruction: prompt.systemInstruction
          }
        };

        // Convert Agent tools to Gemini function schemas
        const geminiTools = this._convertToolsToGeminiSchema(tools);
        if (geminiTools.length > 0) {
          request.tools = [{ functionDeclarations: geminiTools }];
        }

        const response = await this.genAI.models.generateContent(request);

        // Handle tool calls if present in the response
        if (response.candidates && response.candidates[0] && response.candidates[0].content && response.candidates[0].content.parts) {
          const toolCallPart = response.candidates[0].content.parts.find(part => part.functionCall);
          if (toolCallPart) {
            const functionCall = toolCallPart.functionCall;
            // Return a special string that the Agent's ToolHandler can parse
            return `[TOOL: ${functionCall.name}(${JSON.stringify(functionCall.args)})]`;
          }
        }

        return response.text;
      });
    } catch (error) {
      console.error('Error calling Gemini:', error);
      throw error;
    }
  }

  /**
   * Generate content with streaming response
   * @param {Object} prompt - Formatted prompt for the LLM
   * @param {Object} prompt.contents - Array of content parts
   * @param {number} [prompt.temperature] - Temperature for generation (0.0-1.0)
   * @param {number} [prompt.topP] - Top-p sampling parameter
   * @param {number} [prompt.topK] - Top-k sampling parameter
   * @param {number} [prompt.maxOutputTokens] - Maximum output tokens
   * @param {string} [prompt.systemInstruction] - System instruction
   * @param {Function} onChunk - Callback for each chunk of the response
   * @param {Object} [tools={}] - Object containing tool functions (Agent format)
   * @returns {Promise<string>} - Complete LLM response
   * @throws {Error} - If API call fails or quota is exceeded
   */
  async generateContentStream(prompt, onChunk, tools = {}) {
    try {
      return await this.retryWithBackoff(async () => {
        const request = {
          model: this.modelName,
          contents: prompt.contents,
          config: {
            temperature: prompt.temperature || 0.7,
            topP: prompt.topP || 0.95,
            topK: prompt.topK || 40,
            maxOutputTokens: prompt.maxOutputTokens || 1024,
            systemInstruction: prompt.systemInstruction
          }
        };

        // Convert Agent tools to Gemini function schemas
        const geminiTools = this._convertToolsToGeminiSchema(tools);
        if (geminiTools.length > 0) {
          request.tools = [{ functionDeclarations: geminiTools }];
        }

        const response = await this.genAI.models.generateContentStream(request);
        
        let fullResponse = '';
        
        for await (const chunk of response) {
          if (chunk.text) {
            fullResponse += chunk.text;
            if (onChunk) {
              onChunk(chunk.text);
            }
          }
          // Handle tool calls in stream if needed, though typically full tool calls come at the end
          if (chunk.candidates && chunk.candidates[0] && chunk.candidates[0].content && chunk.candidates[0].content.parts) {
            const toolCallPart = chunk.candidates[0].content.parts.find(part => part.functionCall);
            if (toolCallPart) {
              const functionCall = toolCallPart.functionCall;
              fullResponse += `[TOOL: ${functionCall.name}(${JSON.stringify(functionCall.args)})]`;
            }
          }
        }
        
        return fullResponse;
      });
    } catch (error) {
      console.error('Error calling Gemini stream:', error);
      throw error;
    }
  }

  /**
   * Convert Agent tools to Gemini function calling schema
   * @param {Object} tools - Object containing tool functions
   * @returns {Array} - Array of Gemini function declarations
   */
  _convertToolsToGeminiSchema(tools) {
    const functionDeclarations = [];

    for (const [toolName, toolFunction] of Object.entries(tools)) {
      // Create basic schema for each tool
      const functionDeclaration = {
        name: toolName,
        description: this._getToolDescription(toolName, toolFunction),
        parameters: this._getToolParameters(toolName, toolFunction)
      };

      functionDeclarations.push(functionDeclaration);
    }

    return functionDeclarations;
  }

  /**
   * Get tool description for Gemini schema
   * @param {string} toolName - Name of the tool
   * @param {Function} toolFunction - Tool function
   * @returns {string} - Tool description
   */
  _getToolDescription(toolName, toolFunction) {
    // Map common tool names to descriptions
    const descriptions = {
      'travelSearch': 'Search for travel-related information including destinations, accommodations, activities, and current travel data',
      'datetime': 'Get current date/time information and perform date calculations',
      'calculator': 'Perform mathematical calculations for budgeting and planning'
    };

    return descriptions[toolName] || `Execute ${toolName} tool`;
  }

  /**
   * Get tool parameters schema for Gemini
   * @param {string} toolName - Name of the tool
   * @param {Function} toolFunction - Tool function
   * @returns {Object} - Parameters schema
   */
  _getToolParameters(toolName, toolFunction) {
    // Define parameter schemas for known tools
    const parameterSchemas = {
      'travelSearch': {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'Search query for travel information'
          }
        },
        required: ['query']
      },
      'datetime': {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            description: 'Action to perform (get_current_date, calculate_date, etc.)'
          },
          date: {
            type: 'string',
            description: 'Date string for calculations (optional)'
          }
        },
        required: ['action']
      },
      'calculator': {
        type: 'object',
        properties: {
          expression: {
            type: 'string',
            description: 'Mathematical expression to calculate'
          }
        },
        required: ['expression']
      }
    };

    return parameterSchemas[toolName] || {
      type: 'object',
      properties: {
        input: {
          type: 'string',
          description: 'Input for the tool'
        }
      },
      required: ['input']
    };
  }

  /**
   * Helper function to implement retry with exponential backoff
   * @param {Function} operation - Async operation to retry
   * @param {number} maxRetries - Maximum number of retries
   * @param {number} initialDelay - Initial delay in milliseconds
   * @returns {Promise<any>} - Result of the operation
   */
  async retryWithBackoff(operation, maxRetries = 5, initialDelay = 1000) {
    let retries = 0;
    let delay = initialDelay;

    while (retries < maxRetries) {
      try {
        return await operation();
      } catch (error) {
        // Check if this is a server overload error
        const isOverloaded =
          error.message?.includes('UNAVAILABLE') ||
          error.message?.includes('overloaded') ||
          error.message?.includes('503') ||
          (error.status === 503);

        // If it's not an overload error or we've used all retries, throw
        if (!isOverloaded || retries >= maxRetries - 1) {
          throw error;
        }

        // Increment retry counter
        retries++;

        // Log the retry attempt
        console.log(`Model overloaded. Retry attempt ${retries}/${maxRetries} after ${delay}ms delay...`);

        // Wait for the delay period
        await new Promise(resolve => setTimeout(resolve, delay));

        // Exponential backoff with jitter
        delay = Math.min(delay * 2, 30000) * (0.8 + Math.random() * 0.4);
      }
    }
  }
}
