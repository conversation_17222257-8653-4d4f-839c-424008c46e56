/**
 * Event System for Agent Framework
 */
class EventEmitter {
  constructor() {
    this.events = {};
  }

  /**
   * Subscribe to an event
   * @param {string} eventName - Name of the event to subscribe to
   * @param {Function} listener - Function to call when the event is emitted
   * @returns {Function} - Unsubscribe function
   */
  on(eventName, listener) {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }
    this.events[eventName].push(listener);
    
    // Return unsubscribe function
    return () => this.off(eventName, listener);
  }

  /**
   * Unsubscribe from an event
   * @param {string} eventName - Name of the event to unsubscribe from
   * @param {Function} listenerToRemove - Listener function to remove
   */
  off(eventName, listenerToRemove) {
    if (!this.events[eventName]) return;
    
    this.events[eventName] = this.events[eventName].filter(
      listener => listener !== listenerToRemove
    );
  }

  /**
   * Emit an event
   * @param {string} eventName - Name of the event to emit
   * @param {*} data - Data to pass to the listeners
   */
  emit(eventName, data) {
    if (!this.events[eventName]) return;
    
    this.events[eventName].forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error(`Error in event listener for ${eventName}:`, error);
      }
    });
  }

  /**
   * Subscribe to an event for a single occurrence
   * @param {string} eventName - Name of the event to subscribe to
   * @param {Function} listener - Function to call when the event is emitted
   */
  once(eventName, listener) {
    const onceWrapper = (data) => {
      listener(data);
      this.off(eventName, onceWrapper);
    };
    return this.on(eventName, onceWrapper);
  }
}

/**
 * Memory System for Agent Framework
 */
class MemoryManager {
  constructor(config = {}) {
    this.conversationHistory = [];
    this.keyValueStore = {};
    this.maxHistoryLength = config.maxHistoryLength || 100;
    this.events = new EventEmitter();
  }

  /**
   * Add an entry to the conversation history
   * @param {Object} entry - Entry to add to history
   */
  addToHistory(entry) {
    this.conversationHistory.push({
      ...entry,
      timestamp: entry.timestamp || new Date()
    });
    
    // Trim history if it exceeds max length
    if (this.conversationHistory.length > this.maxHistoryLength) {
      this.conversationHistory.shift();
    }
    
    this.events.emit('historyUpdated', this.conversationHistory);
  }

  /**
   * Get the conversation history
   * @param {number} limit - Maximum number of entries to return
   * @returns {Array} - Conversation history
   */
  getHistory(limit = this.maxHistoryLength) {
    return this.conversationHistory.slice(-limit);
  }

  /**
   * Store a value in memory
   * @param {string} key - Key to store the value under
   * @param {*} value - Value to store
   */
  remember(key, value) {
    this.keyValueStore[key] = value;
    this.events.emit('memoryUpdated', { key, value });
  }

  /**
   * Retrieve a value from memory
   * @param {string} key - Key to retrieve
   * @returns {*} - Stored value or undefined
   */
  recall(key) {
    return this.keyValueStore[key];
  }

  /**
   * Clear all memory
   */
  clear() {
    this.conversationHistory = [];
    this.keyValueStore = {};
    this.events.emit('memoryCleared');
  }

  /**
   * Subscribe to memory events
   * @param {string} eventName - Event name to subscribe to
   * @param {Function} listener - Function to call when event is emitted
   * @returns {Function} - Unsubscribe function
   */
  on(eventName, listener) {
    return this.events.on(eventName, listener);
  }
}

/**
 * Interface for an LLM Provider.
 */
class LLMProvider {
  async generateContent(options) {
    throw new Error("Subclasses must implement generateContent.");
  }
}

/**
 * Interface for a Tool Handler.
 */
class ToolHandler {
  async handleToolCalls(responseText, tools, callToolFn, formatToolResultFn) {
    throw new Error("Subclasses must implement handleToolCalls.");
  }
}

/**
 * Default Tool Handler (using [TOOL: ...] syntax).
 */
class DefaultToolHandler extends ToolHandler {
  async handleToolCalls(responseText, tools, callToolFn, formatToolResultFn) {
    const toolCallRegex = /\[TOOL: (\w+)\(([^)]*)\)\]/g;
    let match;
    let processedResponse = responseText;
    const toolCalls = [];

    while ((match = toolCallRegex.exec(responseText)) !== null) {
      const toolName = match[1];
      const toolParams = match[2];
      if (tools[toolName]) {
        toolCalls.push({ toolName, toolParams, fullMatch: match[0] });
      }
    }

    for (const call of toolCalls) {
      try {
        console.log(`Executing tool: ${call.toolName} with params: ${call.toolParams}`);
        const toolResult = await callToolFn(call.toolName, call.toolParams);
        const formattedResult = formatToolResultFn(call.toolName, call.toolParams, toolResult);
        processedResponse = processedResponse.replace(call.fullMatch, formattedResult);
      } catch (error) {
        console.error(`Error executing tool ${call.toolName}:`, error);
        processedResponse = processedResponse.replace(call.fullMatch, `Error executing ${call.toolName}: ${error.message}`);
      }
    }

    return processedResponse;
  }
}

/**
 * Advanced Tool Handler with multiple syntax support and validation
 */
class AdvancedToolHandler extends ToolHandler {
  constructor(config = {}) {
    super();
    this.events = new EventEmitter();
    this.retryAttempts = config.retryAttempts || 3;
    this.retryDelay = config.retryDelay || 1000;
    this.fallbackEnabled = config.fallbackEnabled !== false;
  }

  async handleToolCalls(responseText, tools, callToolFn, formatToolResultFn) {
    const toolCalls = this._extractToolCalls(responseText);
    let processedResponse = responseText;

    for (const call of toolCalls) {
      try {
        const tool = tools[call.toolName];
        if (!tool) {
          throw new Error(`Tool '${call.toolName}' not found`);
        }

        console.log(`Executing tool: ${call.toolName} with params:`, call.params);
        
        const result = await this._executeWithRetry(tool, call.params, callToolFn);
        const formattedResult = formatToolResultFn(call.toolName, call.params, result);
        processedResponse = processedResponse.replace(call.fullMatch, formattedResult);

      } catch (error) {
        console.error(`Error executing tool ${call.toolName}:`, error);
        const errorMessage = this.fallbackEnabled 
          ? `Tool ${call.toolName} unavailable, continuing without it.`
          : `Error executing ${call.toolName}: ${error.message}`;
        
        processedResponse = processedResponse.replace(call.fullMatch, errorMessage);
      }
    }

    return processedResponse;
  }

  _extractToolCalls(responseText) {
    const toolCalls = [];

    // Format 1: [TOOL: toolName(params)]
    const bracketRegex = /\[TOOL:\s*(\w+)\(([^)]*)\)\]/g;
    let match;
    while ((match = bracketRegex.exec(responseText)) !== null) {
      toolCalls.push({
        toolName: match[1],
        params: this._parseParams(match[2]),
        fullMatch: match[0],
        format: 'bracket'
      });
    }

    // Format 2: JSON function calling
    const jsonRegex = /\{"tool":\s*"(\w+)",\s*"parameters":\s*({[^}]+})\}/g;
    while ((match = jsonRegex.exec(responseText)) !== null) {
      try {
        const params = JSON.parse(match[2]);
        toolCalls.push({
          toolName: match[1],
          params,
          fullMatch: match[0],
          format: 'json'
        });
      } catch (e) {
        console.warn('Failed to parse JSON tool call:', match[0]);
      }
    }

    return toolCalls;
  }

  _parseParams(paramString) {
    if (!paramString.trim()) return {};
    
    try {
      return JSON.parse(paramString.replace(/'/g, '"'));
    } catch (e) {
      return { query: paramString.replace(/['"]/g, '') };
    }
  }

  async _executeWithRetry(tool, params, callToolFn, attempt = 1) {
    try {
      if (tool.execute) {
        return await tool.execute(params);
      } else {
        return await callToolFn(tool.name || 'unknown', params);
      }
    } catch (error) {
      if (attempt < this.retryAttempts) {
        console.log(`Tool execution failed, retrying (${attempt}/${this.retryAttempts})...`);
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
        return this._executeWithRetry(tool, params, callToolFn, attempt + 1);
      }
      throw error;
    }
  }
}

/**
 * Base Agent (Composable with LLM Provider, Tool Handler, and Event System).
 */
export class Agent {
  constructor(config) {
    if (!config.id || !config.name || !config.description || !config.role || !config.llmProvider) {
      throw new Error("Agent configuration must include id, name, description, role, and llmProvider.");
    }
    
    // Core properties
    this.id = config.id;
    this.name = config.name;
    this.description = config.description;
    this.role = config.role;
    this.tools = config.tools || {};
    this.toolSchemas = [];
    
    // LLM configuration
    this.llmConfig = {
      temperature: 0.7,
      maxOutputTokens: 1024,
      ...config.llmConfig,
    };
    this.llmProvider = config.llmProvider;
    
    // Component systems - Use AdvancedToolHandler by default
    this.toolHandler = config.toolHandler || new AdvancedToolHandler();
    this.memory = config.memoryManager || new MemoryManager(config.memoryConfig);
    this.events = new EventEmitter();
    
    // Context for current operation
    this.context = {};
    this.status = 'idle';
    this.persona = null;
    
    // Formatter functions
    this.inputFormatter = config.inputFormatter || ((input) => [{ role: "user", parts: [{ text: String(input) }] }]);
    this.responseProcessor = config.responseProcessor || ((llmResponse) => {
      if (llmResponse && llmResponse.candidates && llmResponse.candidates[0] && llmResponse.candidates[0].content && llmResponse.candidates[0].content.parts) {
        return llmResponse.candidates[0].content.parts.map(part => part.text).join('');
      } else if (typeof llmResponse === 'string') {
        return llmResponse;
      }
      return '';
    });
    this.toolResultFormatter = config.toolResultFormatter || ((toolName, toolParams, toolResult) => {
      let formatted = `\n\n### Result of ${toolName}("${toolParams}"):\n\n`;
      if (Array.isArray(toolResult)) {
        formatted += toolResult.map(item => `- ${typeof item === 'object' ? JSON.stringify(item) : item}`).join('\n');
      } else if (typeof toolResult === 'object') {
        formatted += JSON.stringify(toolResult, null, 2);
      } else {
        formatted += String(toolResult);
      }
      formatted += '\n\n';
      return formatted;
    });

    // Initialize tool schemas
    this._refreshToolSchemas();
  }

  /**
   * Get the agent's current status.
   * @returns {string} - The agent's status.
   */
  getStatus() {
    return this.status;
  }

  /**
   * Set the agent's status and emit a status change event.
   * @param {string} newStatus - The new status for the agent.
   */
  setStatus(newStatus) {
    const oldStatus = this.status;
    this.status = newStatus;
    this.events.emit('statusChanged', { 
      agent: this.id, 
      oldStatus, 
      newStatus,
      timestamp: new Date()
    });
  }

  /**
   * Add a tool to the agent and emit a tool added event.
   * @param {string} name - Tool name.
   * @param {Function} tool - Tool function.
   */
  addTool(name, tool) {
    this.tools[name] = tool;
    this.events.emit('toolAdded', { agent: this.id, toolName: name });
  }

  /**
   * Get the available tools for the agent.
   * @returns {Object} - An object containing the agent's tools.
   */
  getTools() {
    return this.tools;
  }

  /**
   * Subscribe to agent events.
   * @param {string} eventName - Event name to subscribe to.
   * @param {Function} listener - Function to call when event is emitted.
   * @returns {Function} - Unsubscribe function.
   */
  on(eventName, listener) {
    return this.events.on(eventName, listener);
  }

  /**
   * Run the agent with a specific input.
   * @param {*} input - Input for the agent to process.
   * @param {Object} [context={}] - Additional context for the agent's operation.
   * @returns {Promise<*>} - Agent's response.
   */
  async run(input, context = {}) {
    if (!this.llmProvider) {
      throw new Error(`Agent ${this.name} has no LLM provider.`);
    }

    this.context = { ...this.context, ...context };
    this.setStatus('working');
    this.events.emit('runStarted', {
      agent: this.id,
      input,
      context: this.context,
      timestamp: new Date()
    });

    let rawToolResult = null; // Variable to store the raw tool result
    let toolWasCalled = false; // Flag to indicate if a tool was called

    try {
      // Format input and call LLM
      const formattedInput = await this.inputFormatter(input, this.tools);
      this.events.emit('inputFormatted', { agent: this.id, formattedInput });

      const llmResponse = await this.llmProvider.generateContent(
        {
          contents: formattedInput,
          systemInstruction: this.role,
          temperature: this.llmConfig.temperature,
          maxOutputTokens: this.llmConfig.maxOutputTokens,
          ...this.llmProviderSpecificConfig(),
        },
        this.tools // Pass the tools to the LLM provider
      );
      this.events.emit('llmResponseReceived', { agent: this.id, llmResponse });

      // Process response and handle tool calls
      let processedResponse = await this.responseProcessor(llmResponse);
      this.events.emit('responseProcessed', { agent: this.id, processedResponse });

      if (Object.keys(this.tools).length > 0 && this.toolHandler) {
        processedResponse = await this.toolHandler.handleToolCalls(
          processedResponse,
          this.tools,
          async (toolName, params) => {
            this.events.emit('toolCalled', { agent: this.id, toolName, params });
            const result = await this.tools[toolName](params); // Executes the tool function
            this.events.emit('toolCompleted', { agent: this.id, toolName, params, result });
            rawToolResult = result; // Store the raw tool result
            toolWasCalled = true; // Set the flag
            return result; // Return the result for formatting in processedResponse
          },
          this.toolResultFormatter
        );
        this.events.emit('toolCallsHandled', { agent: this.id, processedResponse });
      }

      // Store in memory
      this.memory.addToHistory({ input, response: processedResponse, timestamp: new Date() });
      this.setStatus('idle');

      this.events.emit('runCompleted', {
        agent: this.id,
        input,
        response: processedResponse, // Still emit processedResponse for history/logging
        timestamp: new Date()
      });

      // Return the raw tool result if a tool was called, otherwise return the processed LLM response
      if (toolWasCalled) {
        return rawToolResult;
      } else {
        return processedResponse;
      }

    } catch (error) {
      this.setStatus('error');
      this.events.emit('runError', {
        agent: this.id,
        input,
        error: error.message,
        timestamp: new Date()
      });
      console.error(`Agent ${this.name} encountered an error:`, error);
      throw error;
    }
  }

  /**
   * Optional method to provide LLM provider-specific configuration.
   * @returns {Object} - Provider-specific configuration.
   */
  llmProviderSpecificConfig() {
    return {};
  }

  /**
   * Set the agent's persona/reasoning style
   * @param {string} persona - Persona identifier
   */
  setPersona(persona) {
    const personaTemplates = {
      strategist: "You are a strategic thinker who excels at long-term planning and identifying key leverage points. Consider multiple approaches before deciding on the optimal path forward.",
      analyst: "You are a detail-oriented analyst who excels at breaking down complex problems into manageable components. Focus on data and evidence-based reasoning.",
      creative: "You are an innovative thinker who excels at generating novel solutions. Don't be constrained by conventional approaches.",
      // Add more personas as needed
    };
    
    if (!personaTemplates[persona]) {
      throw new Error(`Unknown persona: ${persona}`);
    }
    
    // Enhance the agent's role with the persona
    this.role = `${this.role}\n\n${personaTemplates[persona]}`;
    this.persona = persona;
    
    return this;
  }

  /**
   * Dynamically add a tool at runtime
   */
  addTool(tool) {
    if (tool.name) {
      this.tools[tool.name] = tool;
    } else if (typeof tool === 'function') {
      this.tools[tool.name || 'unnamed_tool'] = tool;
    } else {
      throw new Error('Tool must have a name property or be a named function');
    }
    
    this._refreshToolSchemas();
    
    this.events.emit('toolAdded', { 
      agent: this.id, 
      toolName: tool.name,
      schema: tool.schema
    });
    
    return this;
  }

  /**
   * Remove a tool
   */
  removeTool(toolName) {
    if (this.tools[toolName]) {
      delete this.tools[toolName];
      this._refreshToolSchemas();
      
      this.events.emit('toolRemoved', { 
        agent: this.id, 
        toolName 
      });
    }
    return this;
  }

  /**
   * Refresh tool schemas for LLM provider
   */
  _refreshToolSchemas() {
    this.toolSchemas = Object.values(this.tools)
      .filter(tool => tool.schema)
      .map(tool => tool.schema);
    
    if (this.llmProvider.updateToolSchemas) {
      this.llmProvider.updateToolSchemas(this.toolSchemas);
    }
  }

  /**
   * Get tool by name
   */
  getTool(toolName) {
    return this.tools[toolName];
  }

  /**
   * List all available tools
   */
  listTools() {
    return Object.keys(this.tools);
  }
}

// Export all classes including new ones
export { EventEmitter, MemoryManager, LLMProvider, ToolHandler, AdvancedToolHandler };
