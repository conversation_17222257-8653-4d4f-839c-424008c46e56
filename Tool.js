import { EventEmitter } from './Agent.js';

/**
 * Tool class with schema generation, validation, and monitoring
 */
export class Tool {
  constructor(config) {
    if (!config.name || !config.description || !config.func) {
      throw new Error("Tool configuration must include name, description, and func");
    }
    
    this.name = config.name;
    this.description = config.description;
    this.func = config.func;
    this.schema = config.schema || this._generateSchema();
    this.isAsync = config.isAsync !== false;
    this.timeout = config.timeout || 30000;
    this.validators = config.validators || [];
    this.monitors = config.monitors || [];
    this.events = new EventEmitter();
  }

  /**
   * Auto-generate JSON schema from function signature
   */
  _generateSchema() {
    const funcStr = this.func.toString();
    const params = this._extractParameters(funcStr);
    
    return {
      type: "function",
      function: {
        name: this.name,
        description: this.description,
        parameters: {
          type: "object",
          properties: params.reduce((acc, param) => {
            acc[param] = { type: "string", description: `Parameter: ${param}` };
            return acc;
          }, {}),
          required: params
        }
      }
    };
  }

  _extractParameters(funcStr) {
    const match = funcStr.match(/\(([^)]*)\)/);
    if (!match || !match[1].trim()) return [];
    
    return match[1]
      .split(',')
      .map(param => param.trim().split('=')[0].trim())
      .filter(param => param && param !== '...');
  }

  /**
   * Execute tool with validation and monitoring
   */
  async execute(params) {
    const startTime = Date.now();
    let success = false;
    let result = null;
    let error = null;

    try {
      // Run validators
      for (const validator of this.validators) {
        await validator(params);
      }

      this.events.emit('toolExecutionStarted', {
        toolName: this.name,
        params,
        timestamp: new Date()
      });

      // Execute with timeout
      if (this.isAsync) {
        result = await Promise.race([
          this.func(params),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Tool execution timeout')), this.timeout)
          )
        ]);
      } else {
        result = this.func(params);
      }

      success = true;
      
      this.events.emit('toolExecutionCompleted', {
        toolName: this.name,
        params,
        result,
        duration: Date.now() - startTime,
        timestamp: new Date()
      });

      return result;
    } catch (err) {
      error = err;
      this.events.emit('toolExecutionFailed', {
        toolName: this.name,
        params,
        error: err.message,
        duration: Date.now() - startTime,
        timestamp: new Date()
      });
      throw err;
    } finally {
      // Run monitors
      for (const monitor of this.monitors) {
        try {
          await monitor(this.name, params, Date.now() - startTime, success, result, error);
        } catch (monitorError) {
          console.warn(`Monitor error for tool ${this.name}:`, monitorError);
        }
      }
    }
  }

  /**
   * Add validator function
   */
  addValidator(validator) {
    this.validators.push(validator);
    return this;
  }

  /**
   * Add monitor function
   */
  addMonitor(monitor) {
    this.monitors.push(monitor);
    return this;
  }
}

// Tool decorators/validators
export const toolValidator = (toolName) => (validatorFn) => validatorFn;
export const toolMonitor = (monitorFn) => monitorFn;
