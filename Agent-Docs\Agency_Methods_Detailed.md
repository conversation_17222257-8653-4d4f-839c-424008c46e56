# Agency.js Detailed Methods Documentation

## Constructor

### constructor(config)
**Purpose**: Initializes a new Agency instance with the provided configuration.
**Parameters**:
- `config`: Object containing agency configuration
  - `name`: Agency name
  - `description`: Agency description
  - `memoryConfig`: Optional configuration for memory management

**Behavior**: Sets up core components (agents, team, brief, activeJobs) and enhanced components (events, globalMemory, memoryScopes, jobContexts, workflows, errorHandlers, workflowErrorHandlers, jobSchemas, memoryAccessControl).

## Agent and Team Management

### addAgent(id, agent)
**Purpose**: Registers an agent with the agency.
**Parameters**:
- `id`: String identifier for the agent
- `agent`: Agent instance to add

**Behavior**: Stores the agent in the agents collection and sets up event subscriptions to relay agent events through the agency's event system. Returns the agency instance for method chaining.

### addTeam(id, team)
**Purpose**: Registers a team with the agency.
**Parameters**:
- `id`: String identifier for the team
- `team`: Team instance to add

**Behavior**: Stores the team in the team collection. Returns the agency instance for method chaining.

## Event Management

### on(eventName, listener)
**Purpose**: Subscribes to agency events.
**Parameters**:
- `eventName`: String name of the event to subscribe to
- `listener`: Function to call when the event is emitted

**Returns**: Unsubscribe function that can be called to remove the listener.

### broadcastEvent(eventName, data)
**Purpose**: Broadcasts an event to all subscribers.
**Parameters**:
- `eventName`: String name of the event to broadcast
- `data`: Event data to include

**Behavior**: Emits the event with agency metadata included in the event data.

### subscribeAgent(agentId, eventName, callback)
**Purpose**: Subscribes an agent to an agency event.
**Parameters**:
- `agentId`: String identifier of the agent to subscribe
- `eventName`: String name of the event to subscribe to
- `callback`: Optional function to transform event data before sending to agent

**Returns**: Unsubscribe function that can be called to remove the subscription.

### sendMessage(senderId, recipientId, message)
**Purpose**: Sends a message from one agent to another.
**Parameters**:
- `senderId`: String identifier of the sending agent
- `recipientId`: String identifier of the receiving agent
- `message`: Message content to send

**Behavior**: Validates that both sender and recipient exist, then emits events for the message.

### onMessage(agentId, listener)
**Purpose**: Subscribes an agent to receive messages.
**Parameters**:
- `agentId`: String identifier of the agent to receive messages
- `listener`: Function to call when a message is received

**Returns**: Unsubscribe function that can be called to remove the subscription.

## Memory Management

### createMemoryScope(scopeId, config)
**Purpose**: Creates a new memory scope for sharing data.
**Parameters**:
- `scopeId`: String identifier for the memory scope
- `config`: Optional configuration for the memory manager

**Returns**: The created memory scope.

### getMemoryScope(scopeId)
**Purpose**: Retrieves a memory scope by ID.
**Parameters**:
- `scopeId`: String identifier of the memory scope to retrieve

**Returns**: The memory scope. Throws an error if the scope doesn't exist.

### shareMemoryBetween(sourceId, targetId, keys, accessMode)
**Purpose**: Shares memory between scopes with access control.
**Parameters**:
- `sourceId`: String identifier of the source memory scope
- `targetId`: String identifier of the target memory scope
- `keys`: Array of keys to share (empty array shares all)
- `accessMode`: Access mode ('read-only' or 'read-write')

**Behavior**: Copies values from source to target scope and sets up access control.

### getMemoryAccessInfo(scopeId, key)
**Purpose**: Gets memory access control information.
**Parameters**:
- `scopeId`: String identifier of the memory scope
- `key`: String key to get access information for

**Returns**: Access control information or null if not found.

## Job Management

### createBrief(jobId, briefData)
**Purpose**: Creates a brief for a job.
**Parameters**:
- `jobId`: String identifier for the job
- `briefData`: Object containing brief information

**Returns**: The created brief.

### createJobContext(jobId, initialContext)
**Purpose**: Creates a job context for sharing data between agents working on the same job.
**Parameters**:
- `jobId`: String identifier for the job
- `initialContext`: Optional initial context data

**Returns**: The created job context.

### updateJobContext(jobId, updates)
**Purpose**: Updates a job context.
**Parameters**:
- `jobId`: String identifier for the job
- `updates`: Object containing context updates

**Returns**: The updated job context.

### getJobContext(jobId)
**Purpose**: Gets a job context by ID.
**Parameters**:
- `jobId`: String identifier for the job

**Returns**: The job context. Throws an error if the context doesn't exist.

### assignJob(jobId, assigneeId, assigneeType)
**Purpose**: Assigns a job to an agent or team.
**Parameters**:
- `jobId`: String identifier for the job
- `assigneeId`: String identifier of the agent or team
- `assigneeType`: Type of assignee ('agent' or 'team')

**Returns**: Job information object.

### execute(jobId, additionalInputs)
**Purpose**: Executes a job.
**Parameters**:
- `jobId`: String identifier for the job
- `additionalInputs`: Optional additional input data for the job

**Returns**: Promise resolving to job results.

### retryJob(jobId, maxRetries, additionalInputs)
**Purpose**: Retries a failed job.
**Parameters**:
- `jobId`: String identifier for the job
- `maxRetries`: Maximum number of retry attempts
- `additionalInputs`: Optional additional inputs for the retry

**Returns**: Promise resolving to job results.

### cleanupJob(jobId, keepResults)
**Purpose**: Cleans up resources for a completed job.
**Parameters**:
- `jobId`: String identifier for the job
- `keepResults`: Whether to keep job results in global memory

**Returns**: Boolean indicating success.

## Workflow Management

### executeWorkflow(workflowDefinition, workflowId, initialData)
**Purpose**: Executes a workflow of jobs (sequential, parallel, or conditional).
**Parameters**:
- `workflowDefinition`: Array of workflow step definitions
- `workflowId`: String identifier for the workflow
- `initialData`: Optional initial data for the workflow

**Returns**: Promise resolving to workflow results.

### cleanupWorkflow(workflowId, keepResults)
**Purpose**: Cleans up resources for a completed workflow.
**Parameters**:
- `workflowId`: String identifier for the workflow
- `keepResults`: Whether to keep workflow results in global memory

**Returns**: Boolean indicating success.

## Error Handling

### setErrorHandler(jobId, handlerFn)
**Purpose**: Sets an error handler for a job.
**Parameters**:
- `jobId`: String identifier for the job
- `handlerFn`: Error handler function

### setWorkflowErrorHandler(workflowId, handlerFn)
**Purpose**: Sets an error handler for a workflow.
**Parameters**:
- `workflowId`: String identifier for the workflow
- `handlerFn`: Error handler function

### defineJobSchema(jobId, inputSchema, outputSchema)
**Purpose**: Defines a schema for job inputs and outputs.
**Parameters**:
- `jobId`: String identifier for the job
- `inputSchema`: Schema for job inputs
- `outputSchema`: Schema for job outputs

### _validateAgainstSchema(data, schema)
**Purpose**: Validates data against a schema.
**Parameters**:
- `data`: Data to validate
- `schema`: Schema to validate against

**Returns**: Validation result with isValid flag and errors.

## Workflow Planning and Execution

### planAndExecuteWorkflow(agentId, goal, options)
Plans and optionally executes a workflow from a high-level goal.

**Parameters:**
- `agentId` (string): The ID of the agent that will plan the workflow
- `goal` (string): A high-level description of the goal to achieve
- `options` (object): Configuration options
  - `execute` (boolean): Whether to execute the planned workflow immediately (default: false)
  - `workflowId` (string): Optional custom ID for the workflow
  - `initialData` (object): Optional initial data for the workflow

**Returns:**
- If `execute` is false: An object containing the planned jobs
- If `execute` is true: An object containing the planned jobs and the workflow ID

**Example:**
```javascript
// Plan a workflow without executing it
const planResult = await agency.planAndExecuteWorkflow('planner-agent', 'Research and summarize recent AI developments');
console.log(planResult.plannedJobs);

// Plan and execute a workflow
const result = await agency.planAndExecuteWorkflow('planner-agent', 'Research and summarize recent AI developments', {
  execute: true,
  workflowId: 'research-workflow',
  initialData: { priority: 'high' }
});
console.log(result.plannedJobs);
console.log(result.workflowId);
```

### planJobsFromGoal(agentId, goal)
Plans a series of jobs from a high-level goal using an agent.

**Parameters:**
- `agentId` (string): The ID of the agent that will plan the jobs
- `goal` (string): A high-level description of the goal to achieve

**Returns:**
- An array of planned job IDs

**Example:**
```javascript
const plannedJobs = await agency.planJobsFromGoal('planner-agent', 'Analyze customer feedback and create a report');
console.log(plannedJobs); // ['collect-feedback', 'analyze-sentiment', 'generate-report']
```



## Utility Methods

### formatBriefAsPrompt(brief, inputs)
Formats a job brief as a prompt for an agent, including various sections like overview, background, objective, etc.

**Parameters:**
- `brief` (object): The job brief object
- `inputs` (object): Additional inputs to include in the prompt

**Returns:**
- A formatted prompt string that can be sent to an agent

**Example:**
```javascript
const brief = agency.createBrief('research-job', {
  title: 'Market Research',
  overview: 'Research the current market trends for AI products',
  objective: 'Identify key market opportunities',
  background: 'Our company is planning to launch a new AI product'
});

const prompt = agency.formatBriefAsPrompt(brief, {
  targetMarket: 'enterprise',
  timeframe: 'Q3 2023'
});
```

### extractInputsFromBrief(brief)
Extracts inputs from a job brief.

**Parameters:**
- `brief` (object): The job brief object

**Returns:**
- An object containing extracted inputs from the brief

**Example:**
```javascript
const brief = agency.createBrief('analysis-job', {
  inputs: {
    dataset: 'sales_2023.csv',
    metrics: ['revenue', 'growth'],
    timeframe: 'quarterly'
  }
});

const inputs = agency.extractInputsFromBrief(brief);
console.log(inputs); // { dataset: 'sales_2023.csv', metrics: ['revenue', 'growth'], timeframe: 'quarterly' }
```

### getJobsByStatus(status)
**Purpose**: Gets all jobs with a specific status.
**Parameters**:
- `status`: Status to filter jobs by

**Returns**: Array of jobs with the specified status.

### getJob(jobId)
**Purpose**: Gets a job by ID.
**Parameters**:
- `jobId`: String identifier for the job

**Returns**: The job object or undefined if not found.

